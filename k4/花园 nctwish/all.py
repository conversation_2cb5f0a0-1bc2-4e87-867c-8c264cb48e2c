import requests
import time
import random
import re
from datetime import datetime
from retrying import retry
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import concurrent.futures

# 定义每个成员的商品URL参数
members = {
    "SION": {"eve_no": "43244044", "goods_no": "136522", "grp_no": "43244046"},
    "RIKU": {"eve_no": "43486832", "goods_no": "140275", "grp_no": "43486835"},
    "YUSHI": {"eve_no": "43486832", "goods_no": "140277", "grp_no": "43486835"},
    "RYO": {"eve_no": "43486832", "goods_no": "140276", "grp_no": "43486835"},
    "JAEHEE": {"eve_no": "43486832", "goods_no": "140274", "grp_no": "43486835"},
    "SAKUYA": {"eve_no": "43486832", "goods_no": "140278", "grp_no": "43486835"}
}

# 域名列表
domains = ["jp", "kr", "cn", "www"]

# 构建完整URL列表
urls = {}
for member, params in members.items():
    member_urls = []
    for domain in domains:
        url = f"https://{domain}.ktown4u.com/iteminfo?eve_no={params['eve_no']}&goods_no={params['goods_no']}&grp_no={params['grp_no']}"
        member_urls.append(url)
    urls[member] = member_urls

# URL 简称映射
url_short = {
    0: "jp",
    1: "kr",
    2: "cn",
    3: "glb"
}

# 显示顺序
order = ["cn", "kr", "jp", "glb"]

# 扩展 User-Agent 列表
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0"
]

# 全局状态
previous_quantities = {member: {} for member in members}
log_files = {member: f"{member.lower()}.txt" for member in members}
previous_log_entries = {member: None for member in members}

# 创建高级会话
def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 创建多个会话以轮换使用
sessions = [create_session() for _ in range(8)]

@retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
def fetch_quantity(member, url_index, session_index=0):
    """增强版库存获取函数"""
    try:
        url = urls[member][url_index]
        session = sessions[session_index % len(sessions)]

        headers = {
            "User-Agent": random.choice(user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.ktown4u.com/",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin"
        }

        # 添加随机延迟模拟人类行为
        time.sleep(random.uniform(0.1, 0.3))

        response = session.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # 使用正则表达式精准匹配数字
        match = re.search(r'"quantity":\s*([-\d]+)', response.text)
        if not match:
            raise ValueError("quantity 字段未找到")

        return int(match.group(1))
    except Exception as e:
        print(f"[Error] 获取 {member} {url_short[url_index]} 失败: {str(e)}")
        return None

def log_entry(member, entry):
    """安全的日志写入函数"""
    if entry == previous_log_entries[member]:
        return
    try:
        with open(log_files[member], "a", encoding="utf-8") as f:
            f.write(entry + "\n")
        previous_log_entries[member] = entry
    except Exception as e:
        print(f"日志写入失败 ({member}): {str(e)}")

def log_initial(member, data):
    """记录初始化数据"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    values = []
    total = 0

    for i, short in enumerate(order):
        q = abs(data.get(i, 0))
        values.append(f"{short}: {q}")
        total += q

    entry = f"{current_time}:, " + ", ".join(values) + f", {total}"
    print(f"[{member}] {entry}")
    log_entry(member, entry)

def log_change(member, url_index, old_q, new_q, current_data):
    """记录库存变化"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    short = url_short[url_index]

    abs_old = abs(old_q)
    abs_new = abs(new_q)
    delta = abs_new - abs_old
    total = sum(abs(v) for v in current_data.values())

    entry = f"{current_time}:, {short}, {abs_new}, {delta}, {total}"
    print(f"[{member}] {entry}")
    log_entry(member, entry)

def fetch_member_quantities(member):
    """获取单个成员所有域名的数据"""
    current_data = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        future_to_url = {executor.submit(fetch_quantity, member, url_index, url_index): url_index
                         for url_index in range(len(domains))}

        for future in concurrent.futures.as_completed(future_to_url):
            url_index = future_to_url[future]
            try:
                quantity = future.result()
                if quantity is not None:
                    current_data[url_index] = quantity
            except Exception as e:
                print(f"[Error] 处理 {member} {url_short[url_index]} 结果时出错: {str(e)}")

    return current_data

def fetch_all_members():
    """并行获取所有成员的数据"""
    results = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
        future_to_member = {executor.submit(fetch_member_quantities, member): member
                            for member in members}

        for future in concurrent.futures.as_completed(future_to_member):
            member = future_to_member[future]
            try:
                member_data = future.result()
                results[member] = member_data
            except Exception as e:
                print(f"[Error] 处理 {member} 数据时出错: {str(e)}")

    return results

def main():
    global previous_quantities
    first_round = True
    consecutive_failures = 0
    max_failures = 5

    while True:
        try:
            # 使用并行获取提高效率
            all_members_data = fetch_all_members()

            # 检查数据完整性
            complete_data = True
            for member, data in all_members_data.items():
                if len(data) < len(domains):
                    complete_data = False
                    break

            if complete_data:
                consecutive_failures = 0  # 重置失败计数

                if first_round:
                    for member, data in all_members_data.items():
                        previous_quantities[member] = data.copy()
                        log_initial(member, data)
                    first_round = False
                else:
                    for member, data in all_members_data.items():
                        for url_index in range(len(domains)):
                            if url_index in data:
                                new_q = data[url_index]
                                old_q = previous_quantities[member].get(url_index)
                                if old_q is not None and new_q != old_q:
                                    log_change(member, url_index, old_q, new_q, data)
                        previous_quantities[member] = data.copy()

                # 动态调整休眠时间
                sleep_time = random.uniform(1.5, 3.0)
            else:
                # 部分获取失败时增加等待时间
                consecutive_failures += 1
                sleep_time = min(5 * consecutive_failures, 30)
                print(f"[Warning] 部分数据获取失败，增加等待时间至 {sleep_time}秒")

                # 如果连续多次失败，重新创建会话
                if consecutive_failures >= max_failures:
                    print("[Warning] 连续多次失败，重新创建会话...")
                    sessions.clear()
                    sessions.extend([create_session() for _ in range(8)])
                    consecutive_failures = 0

            time.sleep(sleep_time)

        except Exception as e:
            print(f"[Critical Error] 主循环异常: {str(e)}")
            time.sleep(10)  # 发生严重错误时长时间等待

if __name__ == "__main__":
    main()