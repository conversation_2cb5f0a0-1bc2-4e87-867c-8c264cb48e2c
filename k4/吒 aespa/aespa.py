import requests
import time
import random
import re
from datetime import datetime
from retrying import retry
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import concurrent.futures

# Ktown4u 目标 URL 列表
urls = [
    "https://jp.ktown4u.com/iteminfo?eve_no=43508725&goods_no=140846&grp_no=43508727",
    "https://kr.ktown4u.com/iteminfo?eve_no=43508725&goods_no=140846&grp_no=43508727",
    "https://cn.ktown4u.com/iteminfo?eve_no=43508725&goods_no=140846&grp_no=43508727",
    "https://www.ktown4u.com/iteminfo?eve_no=43508725&goods_no=140846&grp_no=43508727"
]

# URL 简称映射
url_short = {
    urls[0]: "jp",
    urls[1]: "kr",
    urls[2]: "cn",
    urls[3]: "glb"
}

# 显示顺序
order = ["cn", "kr", "jp", "glb"]

# 扩展 User-Agent 列表
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0"
]

# 全局状态
previous_quantities = {}
log_file = "aespa.txt"
previous_log_entry = None

# 创建高级会话
def create_session():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["GET"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 创建多个会话以轮换使用
sessions = [create_session() for _ in range(4)]

@retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
def fetch_quantity(url, session_index=0):
    """增强版库存获取函数"""
    try:
        session = sessions[session_index % len(sessions)]

        headers = {
            "User-Agent": random.choice(user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.ktown4u.com/",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin"
        }

        # 添加随机延迟模拟人类行为
        time.sleep(random.uniform(0.1, 0.3))

        response = session.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # 使用正则表达式精准匹配数字
        match = re.search(r'"quantity":\s*([-\d]+)', response.text)
        if not match:
            raise ValueError("quantity 字段未找到")

        return int(match.group(1))
    except Exception as e:
        print(f"[Error] 获取 {url_short[url]} 失败: {str(e)}")
        return None

def log_entry(entry):
    """安全的日志写入函数"""
    global previous_log_entry
    if entry == previous_log_entry:
        return
    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(entry + "\n")
        previous_log_entry = entry
    except Exception as e:
        print(f"日志写入失败: {str(e)}")

def log_initial(data):
    """记录初始化数据"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    values = []
    total = 0

    for short in order:
        q = abs(next((v for k,v in data.items() if url_short[k] == short), 0))
        values.append(f"{short}: {q}")
        total += q

    entry = f"{current_time}:, " + ", ".join(values) + f", {total}"
    print(entry)
    log_entry(entry)

def log_change(url, old_q, new_q, current_data):
    """记录库存变化"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    short = url_short[url]

    abs_old = abs(old_q)
    abs_new = abs(new_q)
    delta = abs_new - abs_old
    total = sum(abs(v) for v in current_data.values())

    entry = f"{current_time}:, {short}, {abs_new}, {delta}, {total}"
    print(entry)
    log_entry(entry)

def fetch_all_quantities():
    """并行获取所有URL的数据"""
    current_data = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        future_to_url = {executor.submit(fetch_quantity, url, i): url
                         for i, url in enumerate(urls)}

        for future in concurrent.futures.as_completed(future_to_url):
            url = future_to_url[future]
            try:
                quantity = future.result()
                if quantity is not None:
                    current_data[url] = quantity
            except Exception as e:
                print(f"[Error] 处理 {url_short[url]} 结果时出错: {str(e)}")

    return current_data

def main():
    global previous_quantities
    first_round = True
    consecutive_failures = 0
    max_failures = 5

    while True:
        try:
            # 使用并行获取提高效率
            current_data = fetch_all_quantities()

            # 完整获取所有数据时处理
            if len(current_data) == len(urls):
                consecutive_failures = 0  # 重置失败计数

                if first_round:
                    previous_quantities = current_data.copy()
                    log_initial(current_data)
                    first_round = False
                else:
                    for url in urls:
                        new_q = current_data[url]
                        old_q = previous_quantities.get(url)
                        if old_q is not None and new_q != old_q:
                            log_change(url, old_q, new_q, current_data)
                    previous_quantities = current_data.copy()

                # 动态调整休眠时间
                sleep_time = random.uniform(1.5, 3.0)
            else:
                # 部分获取失败时增加等待时间
                consecutive_failures += 1
                sleep_time = min(5 * consecutive_failures, 30)
                print(f"[Warning] 部分数据获取失败，增加等待时间至 {sleep_time}秒")

                # 如果连续多次失败，重新创建会话
                if consecutive_failures >= max_failures:
                    print("[Warning] 连续多次失败，重新创建会话...")
                    sessions.clear()
                    sessions.extend([create_session() for _ in range(4)])
                    consecutive_failures = 0

            time.sleep(sleep_time)

        except Exception as e:
            print(f"[Critical Error] 主循环异常: {str(e)}")
            time.sleep(10)  # 发生严重错误时长时间等待

if __name__ == "__main__":
    main()