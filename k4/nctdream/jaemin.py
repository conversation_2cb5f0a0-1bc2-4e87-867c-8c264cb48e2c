import requests
import time
import random
import re
from datetime import datetime
from retrying import retry

# Ktown4u 目标 URL 列表
urls = [
    "https://jp.ktown4u.com/iteminfo?eve_no=43616926&goods_no=144130&grp_no=43616928",
    "https://kr.ktown4u.com/iteminfo?eve_no=43616926&goods_no=144130&grp_no=43616928",
    "https://cn.ktown4u.com/iteminfo?eve_no=43616926&goods_no=144130&grp_no=43616928",
    "https://www.ktown4u.com/iteminfo?eve_no=43616926&goods_no=144130&grp_no=43616928"
]

# URL 简称映射
url_short = {
    urls[0]: "jp",
    urls[1]: "kr",
    urls[2]: "cn",
    urls[3]: "glb"
}

# 显示顺序
order = ["cn", "kr", "jp", "glb"]

# User-Agent 列表
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1"
]

# 全局状态
previous_quantities = {}
log_file = "k4jaemin.txt"
session = requests.Session()
previous_log_entry = None

@retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
def fetch_quantity(url):
    """增强版库存获取函数"""
    try:
        headers = {
            "User-Agent": random.choice(user_agents),
            "Accept": "application/json, */*",
            "Cache-Control": "no-cache",
        }
        response = session.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # 使用正则表达式精准匹配数字
        match = re.search(r'"quantity":\s*([-\d]+)', response.text)
        if not match:
            raise ValueError("quantity 字段未找到")

        return int(match.group(1))
    except Exception as e:
        print(f"[Error] 获取 {url_short[url]} 失败: {str(e)}")
        return None

def log_entry(entry):
    """安全的日志写入函数"""
    global previous_log_entry
    if entry == previous_log_entry:
        return
    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(entry + "\n")
        previous_log_entry = entry
    except Exception as e:
        print(f"日志写入失败: {str(e)}")

def log_initial(data):
    """记录初始化数据"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    values = []
    total = 0

    for short in order:
        q = abs(next((v for k,v in data.items() if url_short[k] == short), 0))
        values.append(f"{short}: {q}")
        total += q

    entry = f"{current_time}:, " + ", ".join(values) + f", {total}"
    print(entry)
    log_entry(entry)

def log_change(url, old_q, new_q, current_data):
    """记录库存变化"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    short = url_short[url]

    abs_old = abs(old_q)
    abs_new = abs(new_q)
    delta = abs_new - abs_old
    total = sum(abs(v) for v in current_data.values())

    entry = f"{current_time}:, {short}, {abs_new}, {delta}, {total}"
    print(entry)
    log_entry(entry)

def main():
    global previous_quantities
    first_round = True

    while True:
        current_data = {}
        valid_count = 0

        for url in urls:
            time.sleep(random.uniform(0.2, 0.8))  # 随机延迟
            q = fetch_quantity(url)
            if q is not None:
                current_data[url] = q
                valid_count += 1

        # 完整获取所有数据时处理
        if valid_count == len(urls):
            if first_round:
                previous_quantities = current_data.copy()
                log_initial(current_data)
                first_round = False
            else:
                for url in urls:
                    new_q = current_data[url]
                    old_q = previous_quantities.get(url)
                    if old_q is not None and new_q != old_q:
                        log_change(url, old_q, new_q, current_data)
                previous_quantities = current_data.copy()

        time.sleep(15)  # 每轮间隔

if __name__ == "__main__":
    main()