import requests
import time
import random
import re
from datetime import datetime, timedelta
from retrying import retry
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import concurrent.futures
import urllib3

# 定义每个成员的商品URL参数
members = {
    "sign": {"eve_no": "43538132", "goods_no": "141729", "grp_no": "43538135"},
    "video": {"eve_no": "43538132", "goods_no": "141730", "grp_no": "43538135"},
}

# 域名列表
domains = ["jp", "kr", "cn", "www"]

# 构建完整URL列表
urls = {}
for member, params in members.items():
    member_urls = []
    for domain in domains:
        url = f"https://{domain}.ktown4u.com/iteminfo?eve_no={params['eve_no']}&goods_no={params['goods_no']}&grp_no={params['grp_no']}"
        member_urls.append(url)
    urls[member] = member_urls

# URL 简称映射
url_short = {
    0: "jp",
    1: "kr",
    2: "cn",
    3: "glb"
}

# 显示顺序
order = ["cn", "kr", "jp", "glb"]

# 扩展 User-Agent 列表
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0"
]

# 全局状态
previous_quantities = {member: {} for member in members}
log_files = {member: f"{member.lower()}.txt" for member in members}
previous_log_entries = {member: None for member in members}

# 统计数据
stats = {
    "total_requests": 0,
    "successful_requests": 0,
    "failed_requests": 0,
    "success_timestamps": [],  # 用于计算每分钟成功频率
    "last_stats_display": datetime.now(),
    "stats_interval": 60  # 每60秒显示一次统计
}

# 创建高级会话
def create_session():
    session = requests.Session()
    
    # 检查urllib3版本，适配不同版本的参数名称
    retry_kwargs = {
        "total": 3,
        "backoff_factor": 0.5,
        "status_forcelist": [429, 500, 502, 503, 504]
    }
    
    # 根据urllib3版本使用正确的参数名
    urllib3_version = urllib3.__version__.split('.')
    major, minor = int(urllib3_version[0]), int(urllib3_version[1])
    
    if (major > 1) or (major == 1 and minor >= 26):
        # 1.26.0及更高版本使用allowed_methods
        retry_kwargs["allowed_methods"] = ["GET"]
    else:
        # 旧版本使用method_whitelist
        retry_kwargs["method_whitelist"] = ["GET"]
    
    retry_strategy = Retry(**retry_kwargs)
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 创建多个会话以轮换使用
sessions = [create_session() for _ in range(8)]

@retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
def fetch_quantity(member, url_index, session_index=0):
    """增强版库存获取函数"""
    global stats
    stats["total_requests"] += 1
    
    try:
        url = urls[member][url_index]
        session = sessions[session_index % len(sessions)]

        # 更丰富的请求头以模拟真实浏览器
        headers = {
            "User-Agent": random.choice(user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://www.ktown4u.com/",
            "Cache-Control": "max-age=0",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1"
        }

        # 添加随机延迟模拟人类行为
        time.sleep(random.uniform(0.5, 1.5))
        
        # 先访问主页以获取cookies
        try:
            main_domain = url.split("//")[1].split(".")[0]
            main_url = f"https://{main_domain}.ktown4u.com/"
            session.get(main_url, headers=headers, timeout=10)
            time.sleep(random.uniform(0.5, 1.0))
        except Exception:
            pass  # 忽略主页访问失败
        
        # 获取商品页面
        response = session.get(url, headers=headers, timeout=15)
        response.raise_for_status()

        # 使用正则表达式精准匹配数字
        match = re.search(r'"quantity":\s*([-\d]+)', response.text)
        if not match:
            match = re.search(r'<[^>]*id=["\']quantity["\'][^>]*>\s*(\d+)', response.text)
            if not match:
                raise ValueError("quantity 字段未找到")
        
        # 记录成功请求
        stats["successful_requests"] += 1
        stats["success_timestamps"].append(datetime.now())
        
        return int(match.group(1))
    except Exception as e:
        # 记录失败请求
        stats["failed_requests"] += 1
        print(f"[Error] 获取 {member} {url_short[url_index]} 失败: {str(e)}")
        return None

def log_entry(member, entry):
    """安全的日志写入函数"""
    if entry == previous_log_entries[member]:
        return
    try:
        with open(log_files[member], "a", encoding="utf-8") as f:
            f.write(entry + "\n")
        previous_log_entries[member] = entry
    except Exception as e:
        print(f"日志写入失败 ({member}): {str(e)}")

def log_initial(member, data):
    """记录初始化数据"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    values = []
    total = 0

    for i, short in enumerate(order):
        q = abs(data.get(i, 0)) if i in data else 0
        values.append(f"{short}: {q}")
        total += q

    entry = f"{current_time}:, " + ", ".join(values) + f", {total}"
    print(f"[{member}] {entry}")
    log_entry(member, entry)

def log_change(member, url_index, old_q, new_q, current_data):
    """记录库存变化"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    short = url_short[url_index]

    abs_old = abs(old_q)
    abs_new = abs(new_q)
    delta = abs_new - abs_old
    total = sum(abs(v) for v in current_data.values())

    entry = f"{current_time}:, {short}, {abs_new}, {delta}, {total}"
    print(f"[{member}] {entry}")
    log_entry(member, entry)

def update_stats():
    """更新并显示统计数据"""
    now = datetime.now()
    
    # 清理一分钟之前的成功记录
    one_minute_ago = now - timedelta(minutes=1)
    stats["success_timestamps"] = [ts for ts in stats["success_timestamps"] if ts > one_minute_ago]
    
    # 如果到了统计显示间隔
    if (now - stats["last_stats_display"]).total_seconds() >= stats["stats_interval"]:
        success_rate = 0
        if stats["total_requests"] > 0:
            success_rate = (stats["successful_requests"] / stats["total_requests"]) * 100
        
        per_minute = len(stats["success_timestamps"])
        
        print(f"\n[统计] 成功率: {success_rate:.1f}% ({stats['successful_requests']}/{stats['total_requests']}), " 
              f"近1分钟成功次数: {per_minute}, 失败总数: {stats['failed_requests']}\n")
        
        stats["last_stats_display"] = now

def fetch_member_quantities(member):
    """获取单个成员所有域名的数据"""
    current_data = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        future_to_url = {executor.submit(fetch_quantity, member, url_index, url_index): url_index
                         for url_index in range(len(domains))}

        for future in concurrent.futures.as_completed(future_to_url):
            url_index = future_to_url[future]
            try:
                quantity = future.result()
                if quantity is not None:
                    current_data[url_index] = quantity
            except Exception as e:
                print(f"[Error] 处理 {member} {url_short[url_index]} 结果时出错: {str(e)}")

    return current_data

def fetch_all_members():
    """并行获取所有成员的数据"""
    results = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:  # 减少并发数以降低被检测风险
        future_to_member = {executor.submit(fetch_member_quantities, member): member
                            for member in members}

        for future in concurrent.futures.as_completed(future_to_member):
            member = future_to_member[future]
            try:
                member_data = future.result()
                results[member] = member_data
            except Exception as e:
                print(f"[Error] 处理 {member} 数据时出错: {str(e)}")

    return results

def main():
    global previous_quantities
    first_round = True
    consecutive_failures = 0
    max_failures = 5

    print("[系统] 开始监控商品库存，每分钟将显示一次统计信息")

    while True:
        try:
            # 使用并行获取提高效率
            all_members_data = fetch_all_members()
            
            # 更新统计信息
            update_stats()

            # 检查数据完整性
            complete_data = True
            for member, data in all_members_data.items():
                if len(data) < len(domains) // 2:  # 允许部分失败
                    complete_data = False
                    break

            if complete_data:
                consecutive_failures = 0  # 重置失败计数

                if first_round:
                    for member, data in all_members_data.items():
                        previous_quantities[member] = data.copy()
                        log_initial(member, data)
                    first_round = False
                else:
                    for member, data in all_members_data.items():
                        for url_index in range(len(domains)):
                            if url_index in data:
                                new_q = data[url_index]
                                old_q = previous_quantities[member].get(url_index)
                                if old_q is not None and new_q != old_q:
                                    log_change(member, url_index, old_q, new_q, data)
                        previous_quantities[member] = data.copy()

                # 设置固定的刷新频率为8秒
                sleep_time = 8.0
            else:
                # 部分获取失败时增加等待时间
                consecutive_failures += 1
                sleep_time = min(5 * consecutive_failures, 60)  # 增加最大等待时间
                print(f"[Warning] 部分数据获取失败，增加等待时间至 {sleep_time}秒")

                # 如果连续多次失败，重新创建会话
                if consecutive_failures >= max_failures:
                    print("[Warning] 连续多次失败，重新创建会话...")
                    sessions.clear()
                    sessions.extend([create_session() for _ in range(8)])
                    consecutive_failures = 0

            time.sleep(sleep_time)

        except Exception as e:
            print(f"[Critical Error] 主循环异常: {str(e)}")
            time.sleep(30)  # 发生严重错误时长时间等待

if __name__ == "__main__":
    main()