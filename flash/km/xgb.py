from flask import Flask, render_template_string, request, session
import os
import numpy as np
import logging
# 标准库
from datetime import datetime
from collections import deque
# 新增: 用于测量载入/处理耗时
import time

# 定义库存处理类
class InventoryProcessor:
    # --- 新增: 为日期解析增加缓存，避免重复调用 ---
    parse_time_cache = {}

    def __init__(self, threshold=None, highlight_num=40, is_ph_file=False, is_ma=False):
        self.threshold = threshold if threshold is not None else (500 if is_ma else 540 if is_ph_file else 1101)
        self.highlight_num = highlight_num
        self.is_ph_file = is_ph_file
        self.is_ma = is_ma

    @staticmethod
    def parse_time(time_str):
        """带有简单缓存的时间解析函数，加速大量重复解析"""
        time_str = time_str.strip().rstrip(':')
        cached = InventoryProcessor.parse_time_cache.get(time_str)
        if cached is not None:
            return cached

        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"]:
            try:
                dt = datetime.strptime(time_str, fmt)
                InventoryProcessor.parse_time_cache[time_str] = dt
                return dt
            except ValueError:
                continue

        logging.error(f"无法解析日期时间字符串: {time_str}")
        dt = datetime.min
        InventoryProcessor.parse_time_cache[time_str] = dt
        return dt

    def process_queue(self, raw_value, pending, valid, time_window, current_ts):
        """优化: 仅解析一次当前行时间，并在记录中缓存 dt，减少循环中的解析开销"""
        processed_value = int(round(raw_value))
        current_dt = self.parse_time(current_ts)

        if processed_value < 0:  # 销量记录
            qty = abs(processed_value)
            sale_record = {
                'timestamp': current_ts,
                'timestamp_dt': current_dt,  # 缓存 datetime 对象
                'original': qty,
                'remaining': qty,
                'valid': qty <= self.threshold
            }
            pending.append(sale_record)
            if sale_record['valid']:
                valid.append(qty)
            return qty
        else:  # 退款记录
            refund = abs(processed_value)
            for i in range(len(pending) - 1, -1, -1):
                record = pending[i]
                time_diff = (current_dt - record['timestamp_dt']).total_seconds()
                if time_diff <= time_window and record['valid'] and record['original'] == refund:
                    try:
                        valid.remove(refund)
                    except ValueError:
                        pass
                    del pending[i]
                    return refund
            return 0

    def process_data(self, data):
        processed_data = []
        app_pending = deque()
        shop_pending = deque()
        app_valid = []
        shop_valid = []
        total_sales = 0
        app_sales = 0

        for line in data:
            if not line.strip():
                continue
            parts = line.split(',')
            if len(parts) < 5:
                continue
            try:
                ts = parts[0].strip()
                raw_sold = float(parts[2])
                raw_stock = float(parts[4])

                # 筛选并计算总销量
                if abs(raw_sold) < self.threshold:
                    total_sales += -raw_sold
                if abs(raw_stock) < self.threshold:
                    total_sales += -raw_stock

                # 计算 app 端的销量
                if abs(raw_sold) <= self.threshold:
                    if raw_sold < 0:
                        app_sales += abs(raw_sold)
                    else:
                        app_sales -= raw_sold

                self.process_queue(raw_sold, app_pending, app_valid, 1920, ts)
                self.process_queue(raw_stock, shop_pending, shop_valid, 1020, ts)
                filtered = abs(raw_sold) > self.threshold or abs(raw_stock) > self.threshold
                processed_data.append((line, filtered, raw_sold, raw_stock))
            except Exception as e:
                logging.error(f"数据处理失败: {line} - {str(e)}")
                continue

        combined = sorted(app_valid + shop_valid, reverse=True)[:self.highlight_num]
        return processed_data, combined, total_sales, app_sales


    def get_paginated_data(self, full_processed_data, page=1, page_size=100):
        total_items = len(full_processed_data)
        total_pages = (total_items + page_size - 1) // page_size if total_items > 0 else 1
        page = max(1, min(page, total_pages))
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_items)
        return full_processed_data[start_idx:end_idx], total_pages

    @staticmethod
    def calculate_stats(values):
        if not values:
            return {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
        arr = np.array(values)
        return {
            'min': int(np.min(arr).item()),
            'max': int(np.max(arr).item()),
            'median': float(np.median(arr).item()),
            'mean': round(float(np.mean(arr)), 2)
        }

# 配置日志记录
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'  # 请替换为安全的密钥

@app.context_processor
def utility_processor():
    def now():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return dict(abs=abs, enumerate=enumerate, now=now)

file_paths = {
    'riize':'riize.txt',

}

template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐶 小狗包数据</title>
<style>
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: #f0f2f5;
    line-height: 1.6;
}

/* 标题样式 */
.header {
    text-align: center;
    padding: 10px; /* 压缩标题上下内边距 */
    background: #007BFF;
    color: white;
    margin-bottom: 10px; /* 压缩标题底部外边距 */
    border-radius: 10px;
}

.header h1 {
    font-size: 2rem; /* 减小标题字体大小 */
    margin-bottom: 5px; /* 压缩标题内部底部外边距 */
    animation: bounce 1.5s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* 按钮容器 */
.button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px; /* 减小按钮之间的间距 */
    margin: 10px 0; /* 压缩按钮容器上下外边距 */
}

.action-button {
    padding: 8px 16px; /* 减小按钮内边距 */
    background: #28a745;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.action-button:hover {
    background: #218838;
    transform: scale(1.03);
}

/* 数据容器 */
.data-container {
    background: white;
    padding: 10px; /* 压缩数据容器内边距 */
    border-radius: 10px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    margin-bottom: 10px; /* 压缩数据容器底部外边距 */
}

/* 统计信息 */
.stats-card {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* 减小统计卡片最小宽度 */
    gap: 10px; /* 减小统计卡片之间的间距 */
    margin: 10px 0; /* 压缩统计卡片上下外边距 */
}

.stat-item {
    background: #f8f9fa;
    padding: 10px; /* 压缩统计卡片内边距 */
    border-radius: 8px;
    text-align: center;
}

.stat-item h3 {
    color: #6c757d;
    font-size: 1rem; /* 减小统计卡片标题字体大小 */
    margin-bottom: 3px; /* 压缩统计卡片标题底部外边距 */
}

.stat-value {
    font-size: 1.5rem; /* 减小统计卡片数值字体大小 */
    font-weight: bold;
    color: #007BFF;
}

/* 高光数据展示（横向一排十个） */
.highlight-container {
    background: #f8f9fa;
    padding: 10px; /* 压缩高光数据容器内边距 */
    border-radius: 15px;
}

.highlight-title {
    color: #333;
    font-size: 1.2rem; /* 减小高光数据标题字体大小 */
    margin-bottom: 10px; /* 压缩高光数据标题底部外边距 */
    text-align: center;
}

/* 修改 highlight-grid 列数为 20 列 */
.highlight-grid {
    display: grid;
    grid-template-columns: repeat(20, 1fr); /* 20 列更紧凑 */
    gap: 4px;
    justify-content: center;
    max-width: 100%;
}
/* section title 跨越 20 列 */
.section-title {
    grid-column: span 20;
}

.highlight-item {
    width: 100%; /* 充满列宽 */
    height: 30px; /* 减小高光数据项高度 */
    background: #e8f5ff; /* 浅蓝色背景 */
    color: #0077cc; /* 蓝色文字 */
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.8rem; /* 减小高光数据项字体大小 */
    transition: transform 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.highlight-item:hover {
    transform: scale(1.05);
    background: #cce5ff; /* 悬停时稍深一点的蓝色 */
}

/* 分段标题 */
.section-title {
    grid-column: span 10; /* 标题占满十列 */
    text-align: center;
    color: #6c757d;
    margin: 10px 0 5px; /* 压缩分段标题上下外边距 */
    font-size: 1rem; /* 减小分段标题字体大小 */
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px; /* 压缩表格顶部外边距 */
}

.data-table th,
.data-table td {
    padding: 8px; /* 减小表格单元格内边距 */
    text-align: center;
    border: 1px solid #dee2e6;
}

.data-table th {
    background: #007BFF;
    color: white;
    font-weight: bold;
}

.data-table tr:nth-child(even) {
    background: #f8f9fa;
}

/* 状态颜色 */
.valid { background: #d4edda; }
.invalid { background: #ffd7d7; }
.normal { background: #e9ecef; }
/* 修改为红色 */
.positive-change { background: #ffcccc; }

/* 响应式设计 */
@media (max-width: 1024px) {
    .highlight-grid { grid-template-columns: repeat(10, 1fr); }
    .section-title { grid-column: span 10; }
}
@media (max-width: 768px) {
    .highlight-grid { grid-template-columns: repeat(8, 1fr); }
    .section-title { grid-column: span 8; }
}
@media (max-width: 480px) {
    .highlight-grid { grid-template-columns: repeat(5, 1fr); }
    .section-title { grid-column: span 5; }
}

@media (max-width: 480px) {
    .highlight-grid {
        grid-template-columns: repeat(10, 1fr); /* 手机端改为十列 */
    }
    .section-title {
        grid-column: span 10;
    }
    .highlight-item {
        height: 25px; /* 进一步减小小屏幕下高光数据项高度 */
        font-size: 0.7rem; /* 进一步减小小屏幕下高光数据项字体大小 */
    }
    /* 让统计信息在手机端显示在一行 */
    .stats-card {
        display: flex;
        justify-content: space-around;
        align-items: center;
        flex-wrap: wrap;
    }
    .stat-item {
        flex: 1;
        min-width: 100px;
    }
}

/* 输入框样式 */
.form-input {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
}
</style>
</head>
<body>
    {# 防止 NoneType 参与 in 测试 #}
    {% set file_name = file_name or '' %}

    <div class="header">
        <h2>小狗包数据</h2>
    </div>

    <div class="button-group">
        <form method="post">
            {% for name in buttons %}
            <button type="submit" name="{{ name }}" class="action-button">
                {{ name|capitalize }} 数据
            </button>
            {% endfor %}
        </form>
    </div>

    <div class="data-container">
        <div class="stats-card">
            <div class="stat-item">
                <h3>销量 & 数据统计</h3>
                <p style="margin:2px 0;font-size:0.85rem;">{% if file_name in ['sign','photo'] %}总销量: {{ new_total }}{% else %}总销量: {{ total }}{% endif %} | APP: {{ kms_total }}</p>
                {% if stats is not none %}
                <p style="margin:2px 0;font-size:0.8rem;">最小: {{ stats.min }} | 最大: {{ stats.max }}</p>
                <p style="margin:2px 0;font-size:0.8rem;">中位: {{ stats.median }} | 平均: {{ stats.mean }}</p>
                {% endif %}
            </div>

            {% if file_name == 'riize' %}
            <div class="stat-item">
                <h3>1 - 20 数据统计</h3>
                <p>最小值: {{ stats_1_50.min }} | 最大值: {{ stats_1_50.max }}</p>
                <p>中位数: {{ stats_1_50.median }} | 平均数: {{ stats_1_50.mean }}</p>
            </div>
            <div class="stat-item">
                <h3>21 - 40 数据统计</h3>
                <p>最小值: {{ stats_51_250.min }} | 最大值: {{ stats_51_250.max }}</p>
                <p>中位数: {{ stats_51_250.median }} | 平均数: {{ stats_51_250.mean }}</p>
            </div>
            {% endif %}
        </div>

        <div class="highlight-container">
            <h2 class="highlight-title">高光数据 (TOP {{ highlight_num }})</h2>
            <div class="highlight-grid">
                {# 修改高光数据展示逻辑 #}
                {% if file_name in ['alldayproject','kun','yy','ten'] %}
                <div class="section-title">1-15</div>
                {% for i, value in enumerate(highlighted_values) if i < 15 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">16-30</div>
                {% for i, value in enumerate(highlighted_values) if i >= 15 and i < 30 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                
                {% elif file_name in ['doyoung','kickflip'] %}
                <div class="section-title">1-50</div>
                {% for i, value in enumerate(highlighted_values) if i < 50 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">51-200</div>
                {% for i, value in enumerate(highlighted_values) if i >= 51 and i < 200 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                
                {% elif file_name in ['phxice','phsion','phriku','phyushi','phjaehee','phryo','phsakuya','phirene','phseulgi','shuhua','miyeon','yuqi','minnie','soyeon','phdonghai','phguixian','phlite','phlixu','phshiyuan','phyinhe','phyisheng'] %}
                <div class="section-title">1-5</div>
                {% for i, value in enumerate(highlighted_values) if i < 5 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">6-10</div>
                {% for i, value in enumerate(highlighted_values) if i >= 5 and i < 10 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                
                {% elif file_name in ['sion','riku','yushi','jaehee','ryo','sakuya'] %}
                <div class="section-title">1-25</div>
                {% for i, value in enumerate(highlighted_values) if i < 25 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">26-40</div>
                {% for i, value in enumerate(highlighted_values) if i >= 25 and i < 40 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                
                {% elif file_name in ['riize'] %}
                <div class="section-title">1-20</div>
                {% for i, value in enumerate(highlighted_values) if i < 20 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">21-40</div>
                {% for i, value in enumerate(highlighted_values) if i >= 20 and i < 40 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                
                {% else %}
                {# 其他文件的默认展示 #}
                <div class="section-title">1-30</div>
                {% for i, value in enumerate(highlighted_values) if i < 30 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                <div class="section-title">31-50</div>
                {% for i, value in enumerate(highlighted_values) if i >= 30 and i < 50 %}
                    <div class="highlight-item">{{ value }}</div>
                {% endfor %}
                {% endif %}
            </div>
        </div>

        <div class="input-form">
            <form method="post">
                <label for="cut_value">{% if file_name == 'sign' %}输入总销量(含4096)÷的数：{% elif file_name == 'photo' %}输入总销量(含1880)÷的数：{% else %}输入总销量÷的数：{% endif %}</label>
                <input type="number" name="cut_value" id="cut_value" step="0.1" min="0" required class="form-input">
                <button type="submit" class="action-button">计算比例</button>
            </form>
        </div>

        {% if cut is not none %}
        <div class="stat-item" style="background: #fff3cd; margin-top: 20px;">
            <h3>计算结果</h3>
            <div class="stat-value">{{ cut }}</div>
        </div>
        {% endif %}

        <h3>详细数据表格</h3>
        <table class="data-table">
            <thead>
                <tr>
                    <th>变化时间</th>
                    <th>app库存</th>
                    <th>app库存变化</th>
                    <th>微店库存</th>
                    <th>微店库存变化</th>
                </tr>
            </thead>
            <tbody>
                {% for line, filtered, real_sold, real_stock in data %}
                {% set parts = line.split(',') %}
                <tr class="{% if real_sold > 0 and real_stock > 0 %}positive-change{% elif not filtered %}normal{% else %}valid{% endif %}">
                    <td>{{ parts[0] }}</td>
                    <td>{{ parts[1] }}</td>
                    <td>{{ parts[2] }}</td>
                    <td>{{ parts[3] }}</td>
                    <td>{{ parts[4] }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

            <div class="pagination-controls">
                <form method="post" id="paginationForm">
                    <input type="hidden" name="cut_value" value="{{ request.form.get('cut_value', '') }}">
                    <button type="submit" name="page" value="{{ page - 1 }}" {% if page <= 1 %}disabled{% endif %}>上一页</button>
                    <span>第 {{ page }} / {{ total_pages }} 页</span>
                    <button type="submit" name="page" value="{{ page + 1 }}" {% if page >= total_pages %}disabled{% endif %}>下一页</button>
                    <input type="number" name="page" min="1" max="{{ total_pages }}" value="{{ page }}" onchange="this.form.submit()">
                    <select name="page_size" onchange="this.form.submit()">
                        {% for size in [50, 100, 200, 500, 1000, 2000] %}
                        <option value="{{ size }}" {% if page_size == size %}selected{% endif %}>每页{{ size }}条</option>
                        {% endfor %}
                    </select>
                </form>
            </div>
    </div>

    <footer style="text-align: center; padding: 20px; color: #6c757d;">
        <p>数据更新时间: {{ now() }}</p>
    </footer>
</body>
</html>
'''


def parse_time(time_str):
    return InventoryProcessor.parse_time(time_str)


############################
# 文件路径自动解析 & 缓存    #
############################

# 在多目录部署场景下，同名文件可能位于嵌套目录。为了避免 FileNotFoundError，
# 提供一个简单的递归搜索，并对结果进行缓存，后续访问无需再次遍历。

_file_lookup_cache = {}


def _resolve_file_path(file_path: str) -> str:
    """若给定路径不存在，则在当前工作目录递归搜索同名文件，并返回首个匹配绝对路径。"""
    # 直接存在就返回
    if os.path.isfile(file_path):
        return file_path

    # 尝试缓存
    base_name = os.path.basename(file_path)
    cached = _file_lookup_cache.get(base_name)
    if cached and os.path.isfile(cached):
        return cached

    # 递归遍历（深度优先）
    for root, _dirs, files in os.walk('.', topdown=True):
        if base_name in files:
            full = os.path.join(root, base_name)
            _file_lookup_cache[base_name] = full
            return full

    # 未找到，返回原值，后续逻辑会捕获异常
    return file_path


def read_file(file_path):
    # 尝试解析真实路径
    resolved_path = _resolve_file_path(file_path)

    if not os.path.isfile(resolved_path):
        logging.error(f"文件不存在: {file_path}")
        return []
    try:
        mtime = os.path.getmtime(resolved_path)
        if (resolved_path, mtime) in read_file.cache:
            return read_file.cache[(resolved_path, mtime)]
        with open(resolved_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = [line.strip() for line in f if line.strip()]
            lines.sort(key=lambda x: parse_time(x.split(',')[0]))
            read_file.cache = {(resolved_path, mtime): lines}
            return lines
    except Exception as e:
        logging.error(f"文件读取失败: {e}")
        return []


read_file.cache = {}

# 新增全量数据缓存，避免重复计算
full_data_processing_cache = {}


# 找到 process_request 函数
def process_request(buttons, default_file, highlight_num_override=None, start=0, end=None):
    # --- 记录开始时间，用于统计载入/处理耗时 ---
    start_time_perf = time.perf_counter()

    # 选按钮
    selected = next((k for k in buttons if k in request.form), None)
    session['current_file'] = file_paths.get(selected, session.get('current_file', default_file))
    file_to_show = session['current_file']

    # 获取分页参数（新增）
    try:
        page = int(request.form.get('page', request.args.get('page', 1)))
    except ValueError:
        page = 1
    try:
        page_size = int(request.form.get('page_size', request.args.get('page_size', 100)))
    except ValueError:
        page_size = 100

    # === 读取文件并计算阈值、高光数等（新增，必须放在缓存逻辑之前） ===
    # 解析真实路径，避免后续 getmtime 失败
    resolved_path = _resolve_file_path(file_to_show)

    data_lines = read_file(resolved_path)

    # 文件类型特征
    is_ph = 'ph' in file_to_show.lower()
    is_ma = any(kw in file_to_show.lower() for kw in ['ma', 'mark'])

    # 获取当前显示的文件名（去掉扩展名）
    file_name = next((k for k, v in file_paths.items() if v == file_to_show), None)
    if not file_name:
        file_name = os.path.basename(file_to_show).split('.')[0]

    # 用户自定义阈值
    try:
        threshold = int(request.form.get('threshold')) if request.form.get('threshold') else None
    except Exception:
        threshold = None

    # 计算默认高光数量（保持与原逻辑一致）
    if file_name in ['sion','riku','yushi','jaehee','ryo','sakuya','pow']:
        default_hn = 40
    elif file_name in ['phsion','phriku','phyushi','phjaehee','phryo','phsakuya','yuqi','shuhua','minnie','miyeon','soyeon']:
        default_hn = 10
    elif file_name in ['phxice','phirene','phseulgi','phdonghai','phguixian','phlite','phlixu','phshiyuan','phyinhe','phyisheng']:
        default_hn = 10
    elif file_name == 'photo':
        default_hn = 60
    elif file_name == 'riize':
        default_hn = 40
    elif file_name == 'coffee':
        default_hn = 50
    elif file_name in ['mark', 'renjun', 'jaemin', 'haechan', 'chenle', 'jisung', 'jeno']:
        default_hn = 40
    elif is_ph:
        default_hn = 50
    elif file_name in ['sion', 'riku', 'yushi', 'jaehee', 'ryo', 'sakuya','alldayproject']:
        default_hn = 30
    elif file_name in ['doyoung', 'kickflip']:
        default_hn = 200
    else:
        default_hn = 50

    # 最终高光数量
    if highlight_num_override is not None:
        highlight_num = highlight_num_override
    elif request.form.get('highlight_num'):
        try:
            highlight_num = int(request.form['highlight_num'])
        except ValueError:
            highlight_num = default_hn
    else:
        highlight_num = default_hn

    # 创建数据处理器实例
    processor = InventoryProcessor(threshold, highlight_num, is_ph, is_ma)

    # === 下面是缓存键与全量数据处理逻辑（保持不变） ===
    # 构建全量数据缓存键（新增）
    full_data_cache_key = (
        resolved_path,
        threshold,
        highlight_num,
        is_ph,
        is_ma,
        os.path.getmtime(resolved_path)
    )

    # 全量数据处理 & 缓存（新增）
    if full_data_cache_key in full_data_processing_cache:
        full_processed, highlighted, total, kms_total = full_data_processing_cache[full_data_cache_key]
    else:
        full_processed, highlighted, total, kms_total = processor.process_data(data_lines)
        full_data_processing_cache[full_data_cache_key] = (full_processed, highlighted, total, kms_total)
        if len(full_data_processing_cache) > 10:
            full_data_processing_cache.pop(next(iter(full_data_processing_cache)))


    paginated_data, total_pages = processor.get_paginated_data(full_processed, page=page, page_size=page_size)

    highlighted_values = highlighted or []

    # 初始化所有统计变量
    stats = InventoryProcessor.calculate_stats(highlighted)
    stats_1_25 = None
    stats_26_40 = None
    stats_1_5 = None
    stats_6_10 = None
    stats_1_50 = None
    stats_51_250 = None

    # 根据文件名处理不同的统计数据
    if file_name in ['sion','riku','yushi','jaehee','ryo','sakuya']:
        # 计算1-25阶段统计
        if len(highlighted) >= 25:
            stats_1_25 = InventoryProcessor.calculate_stats(highlighted[:25])
        else:
            stats_1_25 = InventoryProcessor.calculate_stats(highlighted)

        # 计算26-40阶段统计
        if len(highlighted) >= 25:
            stats_26_40 = InventoryProcessor.calculate_stats(highlighted[25:40])
        elif len(highlighted) > 40:
            stats_26_40 = InventoryProcessor.calculate_stats(highlighted[25:])

    elif file_name in ['phsion','phriku','phyushi','phjaehee','phryo','phsakuya','phirene','phseulgi','shuhua','miyeon','yuqi','minnie','soyeon','phdonghai','phguixian','phlite','phlixu','phshiyuan','phyinhe','phyisheng']:
        # 计算1-5阶段统计
        if len(highlighted) >= 5:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted[:5])
        else:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted)

        # 计算6-10阶段统计
        if len(highlighted) >= 10:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:10])
        elif len(highlighted) > 5:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:])

    elif file_name == 'riize':
        # 计算1-60阶段统计
        if len(highlighted) >= 20:
            stats_1_50 = InventoryProcessor.calculate_stats(highlighted[:20])
        else:
            stats_1_50 = InventoryProcessor.calculate_stats(highlighted)

        # 计算61-260阶段统计
        if len(highlighted) >= 40:
            stats_51_250 = InventoryProcessor.calculate_stats(highlighted[20:40])
        elif len(highlighted) > 20:
            stats_51_250 = InventoryProcessor.calculate_stats(highlighted[20:])

    # 计算总销量
    if file_name in ['suhua','meiyan']:
        new_total = total + 210
    elif file_name == 'riize':
        new_total = total + 99
    elif file_name == '1':
        new_total = total + 1880
    else:
        new_total = total


    if file_name != 'sign' and start is not None and end is not None and paginated_data:
        paginated_data = paginated_data[start:end]

    # 除法计算
    cut = None
    if 'cut_value' in request.form:
        try:
            dv = float(request.form['cut_value'])
            # 针对 sign 和 photo 数据，使用包含额外销量的总销量进行计算
            if file_name == 'sign' or file_name == 'photo':
                cut = new_total / dv if dv != 0 else "除零错误"
            else:
                cut = total / dv if dv != 0 else "除零错误"
        except:
            cut = "无效输入"

    # --- 计算并传递载入时间 ---
    load_time_ms = int((time.perf_counter() - start_time_perf) * 1000)

    return render_template_string(template,
                                  buttons=buttons,
                                  data=paginated_data,  # 使用分页后的数据
                                  highlighted_values=highlighted_values,
                                  stats=stats,
                                  total=int(total),
                                  kms_total=int(kms_total),
                                  highlight_num=highlight_num,
                                  cut=cut,
                                  file_name=file_name,
                                  stats_1_25=stats_1_25,
                                  stats_26_40=stats_26_40,
                                  stats_1_5=stats_1_5,
                                  stats_6_10=stats_6_10,
                                  stats_1_50=stats_1_50,
                                  stats_51_250=stats_51_250,
                                   start=start,
                                  end=end,
                                  new_total=new_total,
                                  # 新增: 载入时间 (毫秒)
                                  load_time=load_time_ms,
                                  page=page,
                                  total_pages=total_pages,
                                  page_size=page_size)


@app.route('/xgb018', methods=['GET', 'POST'])
def xgb018_data():
    buttons = list(file_paths.keys())
    return process_request(buttons, 'phlite.txt')

@app.route('/riize', methods=['GET', 'POST'])
def riize_data():
    return process_request(['riize'], 'riize.txt')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1177, debug=True)