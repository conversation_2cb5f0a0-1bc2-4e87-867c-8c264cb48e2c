from flask import Flask, render_template_string, request
import os
import numpy as np
import logging
import re
from datetime import datetime

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'

# 文件路径配置
file_paths = {
    'sign':'sign.txt',
    'video':'video.txt'
}

order = ["cn", "kr", "jp", "glb"]

HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小狗包，请勿外传</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <style>
       .positive {
            color: green;
            font-weight: 600;
        }
       .negative {
            color: red;
            font-weight: 600;
        }
       .highlight {
            background-color: #d4f7dc !important;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <div class="container mx-auto p-4">
        <div class="bg-blue-500 text-white p-8 rounded-lg shadow-md text-center mb-8">
            <h1 class="text-4xl font-bold">📈 小狗包数据表格 </h1>
            <div class="flex flex-wrap justify-center gap-4 mt-4">
                <button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md" onclick="window.location.reload()">
                    <i class="fa-solid fa-rotate-right"></i> 刷新
                </button>
                <form class="flex flex-wrap gap-4" method="post">
                    <input class="border border-blue-500 py-2 px-4 rounded-md flex-grow" 
                           type="number" 
                           name="divisor" 
                           step="0.01" 
                           min="0.01" 
                           placeholder="输入总销÷" 
                           required>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md" type="submit">
                        <i class="fa-solid fa-calculator"></i> 计算
                    </button>
                </form>
            </div>
            {% if calculation_result %}
            <div class="bg-blue-100 text-blue-800 p-4 rounded-md mt-4">
                计算结果：{{ latest_total }} ÷ {{ request.form.divisor }} = {{ calculation_result|round(2) }}
            </div>
            {% endif %}
        </div>

        <div class="bg-white p-8 rounded-lg shadow-md mb-8">
            <h2 class="text-2xl font-bold mb-2">当前总销量：{{ latest_total }}</h2>
            <p class="mb-4">高光数据数量：{{ highlight_num }}</p>
            <div class="bg-gray-100 p-4 rounded-md text-center flex flex-wrap justify-around">
                <div>
                    <div>最小值</div>
                    <div class="text-blue-500 text-lg font-bold">{{ top_forty_stats.min }}</div>
                </div>
                <div>
                    <div>最大值</div>
                    <div class="text-blue-500 text-lg font-bold">{{ top_forty_stats.max }}</div>
                </div>
                <div>
                    <div>平均值</div>
                    <div class="text-blue-500 text-lg font-bold">{{ top_forty_stats.mean }}</div>
                </div>
                <div>
                    <div>中位数</div>
                    <div class="text-blue-500 text-lg font-bold">{{ top_forty_stats.median }}</div>
                </div>
            </div>
        </div>

        <div class="bg-white p-8 rounded-lg shadow-md">
            <div class="overflow-x-auto">
                <table class="w-full border-collapse">
                    <thead>
                        <tr class="bg-blue-500 text-white">
                            <th class="py-2 px-4 text-left">时间</th>
                            <th class="py-2 px-4 text-left">地区</th>
                            <th class="py-2 px-4 text-left">销量变化</th>
                            <th class="py-2 px-4 text-left">当前总销</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in display_data %}
                        <tr class="{{ 'highlight' if record.delta in highlighted_values }}">
                            <td class="py-2 px-4 whitespace-nowrap">{{ record.time }}</td>
                            <td class="py-2 px-4">{{ record.region.upper() }}</td>
                            <td class="py-2 px-4 {{ 'positive' if record.delta > 0 else 'negative' }}">
                                {{ record.delta }}
                            </td>
                            <td class="py-2 px-4">{{ record.total }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
'''


class SalesProcessor:
    def __init__(self, highlight_num=40):
        self.highlight_num = highlight_num
        self.latest_total = 0  # 最新总量
        self.display_data = []
        self.highlighted_values = []
        self.stats = {}
        self.top_forty_stats = {}
        self.valid_sales = []

    def _parse_datetime(self, raw_time):
        """解析原始时间为datetime对象"""
        try:
            clean_time = raw_time.strip().rstrip(':')
            dt_format = "%Y-%m-%d %H:%M:%S" if clean_time.count(':') == 2 else "%Y-%m-%d %H:%M"
            return datetime.strptime(clean_time, dt_format)
        except Exception as e:
            logging.warning(f"时间解析失败: {raw_time} - {str(e)}")
            return datetime.min

    def process_file(self, lines):
        records = []
        max_datetime = datetime.min

        # 第一阶段：解析所有记录
        for line in lines:
            line = line.strip()
            if not line:
                continue

            parts = [p.strip() for p in re.split(r',|，', line)]
            if len(parts) >= 5:
                try:
                    # 解析时间
                    dt = self._parse_datetime(parts[0])

                    # 解析地区
                    region = parts[1].lower().strip(' :')
                    if region not in order:
                        continue

                    # 解析数值
                    delta = int(parts[3])
                    total = int(parts[4])

                    record = {
                        'datetime': dt,
                        'time': dt.strftime("%m-%d %H:%M"),
                        'region': region,
                        'delta': delta,
                        'total': total,
                        'raw_line': line
                    }
                    records.append(record)

                    # 跟踪最新总量
                    if dt > max_datetime:
                        max_datetime = dt
                        self.latest_total = total

                    # 记录有效销售（正数变化）
                    if delta > 0:
                        self.valid_sales.append(delta)

                except Exception as e:
                    logging.error(f"记录处理失败: {line} - {str(e)}")

        # 按时间倒序排序
        records.sort(key=lambda x: x['datetime'], reverse=True)
        self.display_data = [{
            'time': r['time'],
            'region': r['region'],
            'delta': r['delta'],
            'total': r['total'],
            'raw_line': r['raw_line']
        } for r in records]

        # 处理高光数据（取前N个有效销售）
        sorted_sales = sorted(self.valid_sales, reverse=True)[:self.highlight_num]
        self.highlighted_values = sorted_sales
        self._calculate_stats()
        self._calculate_top_forty_stats()

    def _calculate_stats(self):
        """计算统计数据"""
        if not self.valid_sales:
            self.stats = {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
            return

        arr = np.array(self.valid_sales)
        self.stats = {
            'min': int(np.min(arr)),
            'max': int(np.max(arr)),
            'median': float(np.median(arr)),
            'mean': round(float(np.mean(arr)), 2)
        }

    def _calculate_top_forty_stats(self):
        """计算前四十个数据的统计信息"""
        top_forty = sorted(self.valid_sales, reverse=True)[:self.highlight_num]
        if not top_forty:
            self.top_forty_stats = {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
            return

        arr = np.array(top_forty)
        self.top_forty_stats = {
            'min': int(np.min(arr)),
            'max': int(np.max(arr)),
            'median': float(np.median(arr)),
            'mean': round(float(np.mean(arr)), 2)
        }


def process_request(file_key):
    try:
        file_path = file_paths.get(file_key)
        if not file_path or not os.path.exists(file_path):
            return f"文件 {file_key} 不存在", 404

        # 根据不同的 file_key 设置不同的 highlight_num
        if file_key in ['video', 'minnie', 'soyeon', 'suhua', 'miyeon']:
            highlight_num = 25
        elif file_key == 'sign':
            highlight_num = 30
        else:
            highlight_num = 40

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]

        processor = SalesProcessor(highlight_num=highlight_num)
        processor.process_file(lines)

        # 处理计算请求
        calculation_result = None
        if request.method == 'POST' and 'divisor' in request.form:
            # 确保使用当前文件的最新总量进行计算
            try:
                divisor = float(request.form['divisor'])
                if divisor != 0:
                    calculation_result = processor.latest_total / divisor
            except (ValueError, ZeroDivisionError):
                calculation_result = "无效输入"

        return render_template_string(HTML_TEMPLATE,
                                      latest_total=processor.latest_total,
                                      stats=processor.stats,
                                      top_forty_stats=processor.top_forty_stats,
                                      display_data=processor.display_data,
                                      highlighted_values=processor.highlighted_values,
                                      total_records=len(processor.display_data),
                                      calculation_result=calculation_result,
                                      highlight_num=highlight_num
                                      )

    except Exception as e:
        logging.error(f"请求处理失败: {str(e)}")
        return f"服务器错误: {str(e)}", 500


@app.route('/k4sign011', methods=['GET', 'POST'])
def k4msign01_monitor():
    file_key = 'k4sign'
    return process_request(file_key)

@app.route('/video', methods=['GET', 'POST'])
def aespa_monitor():
    file_key = 'video'
    return process_request(file_key)

@app.route('/sign', methods=['GET', 'POST'])
def sign_monitor():
    file_key = 'sign'
    return process_request(file_key)


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8877, debug=True)
