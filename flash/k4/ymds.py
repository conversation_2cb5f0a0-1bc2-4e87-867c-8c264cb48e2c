from flask import Flask, render_template_string, request
import os
import logging
import re
from datetime import datetime, timedelta
from collections import defaultdict

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', mode='w'),  # 使用w模式，每次运行覆盖之前的日志
        logging.StreamHandler()
    ]
)
logging.info("应用启动")
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'

# 文件路径配置
file_paths = {
    'k4mark':'k4mark.txt',
    'k4jisung':'k4jisung.txt',
    'k4jaemin':'k4jaemin.txt',
    'k4jeno':'k4jeno.txt',
    'k4chenle':'k4chenle.txt',
    'chanyeolday1':'chanyeolday1.txt',
    'chanyeolday2':'chanyeolday2.txt',
    'somi':'somi.txt',

}

order = ["cn", "kr", "jp", "glb"]
TWENTY_FOUR_HOURS = timedelta(hours=24)  # 24小时时间窗口

# 重新添加 HTML_TEMPLATE 定义
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圆梦大使</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#10B981', // 主色调：绿色
                        secondary: '#059669', // 次要绿色
                        accent: '#047857', // 强调绿色
                        light: '#ECFDF5', // 浅色背景
                        dark: '#065F46', // 深色文本
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .card-shadow {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }
            .stats-card {
                @apply bg-white rounded-lg p-4 text-center transition-all duration-300 hover:shadow-lg;
            }
            .btn-primary {
                @apply bg-primary hover:bg-secondary text-white font-medium py-2 px-4 rounded-md transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
            }
            .btn-refresh {
                @apply flex items-center justify-center;
            }
            .highlight {
                @apply bg-green-100;
            }
            .positive {
                @apply text-green-600 font-medium;
            }
            .negative {
                @apply text-red-500 font-medium;
            }
            .table-responsive {
                @apply overflow-x-auto;
            }
            .mobile-table {
                @apply w-full text-sm text-left;
            }
            .mobile-table thead {
                @apply text-xs uppercase bg-primary text-white;
            }
            .mobile-table th, .mobile-table td {
                @apply py-2 px-3;
            }
            .mobile-table tbody tr {
                @apply border-b hover:bg-gray-50 transition-colors duration-150;
            }
            .mobile-table tbody tr:last-child {
                @apply border-0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 min-h-screen flex flex-col">


    <!-- 主内容区 -->
    <main class="flex-grow container mx-auto px-4 py-6">
        <!-- 控制面板 -->
        <section class="bg-white rounded-xl shadow-lg p-6 mb-6 transform transition-all duration-300 hover:shadow-xl">
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div>
                    <p class="text-2xl font-bold text-gray-800">圆梦大使k4</p>
        </div>
                <div class="flex flex-col sm:flex-row gap-1 w-full md:w-auto">
                    <button class="btn-primary btn-refresh text-xs py-1 px-2" onclick="window.location.reload()">
                        <i class="fa-solid fa-rotate-right"></i>
                    </button>
                    <form class="flex flex-grow sm:flex-grow-0 gap-1 w-full sm:w-auto" method="post">
                        <input class="border border-green-300 rounded-md px-1 py-0.5 text-xs focus:outline-none focus:ring-1 focus:ring-green-300 flex-grow" 
                               type="number" 
                               name="divisor" 
                               step="0.01" 
                               min="0.01" 
                               placeholder="÷" 
                               required>
                        <button class="btn-primary text-xs py-0.5 px-1" type="submit">
                            <i class="fa-solid fa-calculator"></i>
                        </button>
    </form>
            </div>
            </div>
            
            {% if calculation_result %}
            <div class="mt-2 bg-green-50 border border-green-200 rounded-lg p-2">
                <p class="text-green-800 text-xs"><i class="fa-solid fa-calculator mr-1"></i> {{ latest_total }} ÷ {{ request.form.divisor }} = {{ calculation_result|round(2) }}</p>
                    </div>
                    {% endif %}
        </section>

        <!-- 数据概览与高光数据统计 -->
        <section class="bg-white rounded-lg shadow p-2 mb-2">
            <h2 class="text-sm font-bold text-gray-800 mb-1">数据统计</h2>
            
            <!-- 基础数据概览 -->
            <div class="grid grid-cols-4 gap-0.5 mb-1">
                <div class="stats-card border-l-2 border-primary py-0 px-0.5">
                    <h3 class="text-xs font-medium text-gray-500 leading-none">总销</h3>
                    <p class="text-sm font-bold text-primary leading-none">{{ latest_total }}</p>
                        </div>
                <div class="stats-card border-l-2 border-secondary py-0 px-0.5">
                    <h3 class="text-xs font-medium text-gray-500 leading-none">高光</h3>
                    <p class="text-sm font-bold text-secondary leading-none">{{ highlight_num }}</p>
                    </div>
                <div class="stats-card border-l-2 border-accent py-0 px-0.5">
                    <h3 class="text-xs font-medium text-gray-500 leading-none">数据个数</h3>
                    <p class="text-sm font-bold text-accent leading-none">{{ total_records }}</p>
                        </div>
                <div class="stats-card border-l-2 border-green-400 py-0 px-0.5">
                    <h3 class="text-xs font-medium text-gray-500 leading-none">更新</h3>
                    <p class="text-sm font-bold text-green-600 leading-none">
                        {% if display_data %}
                            {{ display_data[0].time }}
                        {% else %}
                            --
                    {% endif %}
                    </p>
                        </div>
                    </div>
            
            <!-- 高光数据统计 -->
            <div class="grid grid-cols-4 gap-0.5 mb-1">
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">min</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ top_forty_stats.min }}</div>
                    </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">max</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ top_forty_stats.max }}</div>
                </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">mean</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ top_forty_stats.mean }}</div>
                    </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">middle</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ top_forty_stats.median }}</div>
                </div>
                        </div>
            
            <!-- 高光数据详情 -->
            <div>
                <div class="grid grid-cols-10 gap-0.5">
                    {% for value in sorted_highlights[:highlight_num] %}
                    <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-800 text-xs font-medium">{{ value }}</span>
                            {% endfor %}
                        </div>
                    </div>
        </section>

        <!-- 数据表格 -->
        <section class="bg-white rounded-lg shadow p-2">
            <div class="flex justify-between items-center mb-0.5">
                <h2 class="text-sm font-bold text-gray-800">销售明细</h2>
                <div class="text-xs text-gray-500">
                    <i class="fa-solid fa-circle text-green-500 mr-0.5"></i>高光
                        </div>
                    </div>
            <div class="table-responsive">
                <table class="mobile-table">
                    <thead>
                        <tr>
                            <th class="py-0.5 px-1 text-xs">时间</th>
                            <th class="py-0.5 px-1 text-xs">地区</th>
                            <th class="py-0.5 px-1 text-xs">销量</th>
                            <th class="py-0.5 px-1 text-xs">总销</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in display_data %}
                        <tr class="{{ 'highlight' if record.delta > 0 and record.delta in highlighted_values[:highlight_num] and record.no_returns else '' }}">
                            <td class="py-0.5 px-1">
                                <span class="font-medium text-xs leading-none">{{ record.time }}</span>
                            </td>
                            <td class="py-0.5 px-1">
                                <span class="px-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 leading-none">
                                    {{ record.region.upper() }}
                                </span>
                            </td>
                            <td class="py-0.5 px-1">
                                <span class="{{ 'positive' if record.delta > 0 else 'negative' }} text-xs leading-none">
                                    {{ record.delta }}
                                </span>
                            </td>
                            <td class="py-0.5 px-1 font-medium text-xs leading-none">{{ record.total }}</td>
                        </tr>
                            {% endfor %}
                    </tbody>
                </table>
                        </div>
        </section>

        <!-- 各地区销售统计 -->
        <section class="bg-white rounded-lg shadow p-2 mt-2 mb-2">
            <h2 class="text-sm font-bold text-gray-800 mb-1">各地区统计</h2>
            <div class="grid grid-cols-4 gap-0.5">
                {% for region, stats in region_stats.items() %}
                <div class="stats-card border-l-2 border-{{ loop.cycle('primary', 'secondary', 'accent', 'green-400') }} py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">{{ region.upper() }}</div>
                    <div class="text-sm font-bold text-{{ loop.cycle('primary', 'secondary', 'accent', 'green-400') }} leading-none">{{ stats.total }}</div>
                    <div class="text-xs text-gray-500 leading-none">
                        {{ stats.percentage }}% | {{ stats.count }}
                    </div>
                        </div>
                            {% endfor %}
                        </div>
        </section>
        
        <!-- 销售趋势统计 -->
        <section class="bg-white rounded-lg shadow p-2 mb-2">
            <h2 class="text-sm font-bold text-gray-800 mb-1">销售趋势</h2>
            <div class="grid grid-cols-4 gap-0.5">
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">24h销</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ daily_stats.total_sales }}</div>
                    </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">有效</div>
                    <div class="text-sm font-bold text-primary leading-none">{{ daily_stats.valid_orders }}</div>
                        </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">退单</div>
                    <div class="text-sm font-bold text-red-500 leading-none">{{ daily_stats.return_orders }}</div>
                    </div>
                <div class="stats-card py-0 px-0.5">
                    <div class="text-xs text-gray-500 leading-none">净增</div>
                    <div class="text-sm font-bold text-{{ 'green-600' if daily_stats.net_growth >=0 else 'red-500' }} leading-none">{{ daily_stats.net_growth }}</div>
                        </div>
                    </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-6 mt-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                        </div>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">
                    </a>
                    </div>
                        </div>
                    </div>
    </footer>

    <script>
        // 移动端菜单切换
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // 表格行悬停效果增强
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('shadow-sm');
                this.style.transform = 'translateY(-1px)';
                this.style.transition = 'all 0.2s ease';
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('shadow-sm');
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
'''


class SalesProcessor:
    def __init__(self, highlight_num=40):
        self.highlight_num = highlight_num
        self.latest_total = 0  # 最新总量
        self.display_data = []  # 展示的数据
        self.highlighted_values = []  # 高光数据（24小时内且未被退回）
        self.top_forty_stats = {}  # 高光数据统计
        self.daily_stats = {  # 24小时统计数据
            'total_sales': 0,
            'valid_orders': 0,
            'return_orders': 0,
            'net_growth': 0
        }
        self.region_stats = {}  # 各地区统计
        self.valid_sales = []  # 存储所有有效销售记录（带时间戳和地区）
        self.top_ten_highlights = []  # 高光数据前十
        self.sorted_highlights = []  # 排序后的高光数据

    def _parse_datetime(self, raw_time):
        """解析原始时间为datetime对象"""
        try:
            clean_time = raw_time.strip().rstrip(':')
            dt_format = "%Y-%m-%d %H:%M:%S" if clean_time.count(':') == 2 else "%Y-%m-%d %H:%M"
            return datetime.strptime(clean_time, dt_format)
        except Exception as e:
            logging.warning(f"时间解析失败: {raw_time} - {str(e)}")
            return datetime.min

    def process_file(self, lines):
        records = []
        max_datetime = datetime.min
        self.valid_sales = []  # 重置有效销售数据
        region_data = {region: [] for region in order}  # 按地区存储数据

        # 第一阶段：解析所有记录
        for line in lines:
            line = line.strip()
            if not line:
                continue

            parts = [p.strip() for p in re.split(r',|，', line)]
            if len(parts) >= 5:
                try:
                    # 解析时间
                    dt = self._parse_datetime(parts[0])

                    # 解析地区
                    region = parts[1].lower().strip(' :')
                    if region not in order:
                        continue

                    # 解析数值
                    delta = int(parts[3])
                    total = int(parts[4])

                    record = {
                        'datetime': dt,
                        'time': dt.strftime("%m-%d %H:%M"),
                        'region': region,
                        'delta': delta,
                        'total': total,
                        'no_returns': True,  # 默认设置为没有退单，后续会更新
                    }
                    records.append(record)
                    region_data[region].append(record)

                    # 跟踪最新总量
                    if dt > max_datetime:
                        max_datetime = dt
                        self.latest_total = total

                    # 记录有效销售（带时间戳和地区）
                    if delta > 0:
                        self.valid_sales.append((dt, delta, region))
                except Exception as e:
                    logging.error(f"记录处理失败: {line} - {str(e)}")

        # 按时间倒序排序
        records.sort(key=lambda x: x['datetime'], reverse=True)
        self.display_data = records

        # 找到数据中的最新时间点，用于模拟"现在"的时间点
        simulated_now = max_datetime if max_datetime > datetime.min else datetime.now()

        # 第二阶段：处理24小时内数据和高光数据
        self._process_highlights(simulated_now)
        self._calculate_top_forty_stats()
        self._calculate_daily_stats(simulated_now)
        self._calculate_region_stats()

    def _process_highlights(self, simulated_now=None):
        """处理高光数据，过滤重复退回"""
        now = simulated_now or datetime.now()
        twenty_four_hours_ago = now - TWENTY_FOUR_HOURS

        # 记录当前处理的时间点（调试用）
        logging.info(f"处理高光数据：当前时间={now}, 24小时前={twenty_four_hours_ago}")

        # 第一步：收集所有销售记录和退单记录
        sales_records = []  # (时间, 地区, 金额)
        return_records = []  # (时间, 地区, 金额)

        # 收集所有销售记录（正数）和退单记录（负数取绝对值）
        for record in self.display_data:
            if record['delta'] > 0:  # 销售记录
                sales_records.append((record['datetime'], record['region'], record['delta']))
            elif record['delta'] < 0:  # 退单记录
                return_records.append((record['datetime'], record['region'], abs(record['delta'])))

        # 按时间排序销售记录（从旧到新）
        sales_records.sort(key=lambda x: x[0])

        # 第二步：为每个销售记录查找匹配的退单
        valid_highlights = []

        # 遍历所有销售记录
        for sale_time, sale_region, sale_amount in sales_records:
            # 检查是否有匹配的退单记录
            has_return = False

            # 查找在销售时间后24小时内的退单记录
            for return_time, return_region, return_amount in return_records:
                # 检查是否在有效时间窗口内
                if return_time < sale_time:
                    continue  # 退单在销售之前，跳过

                # 检查是否在24小时窗口内
                if return_time > sale_time + TWENTY_FOUR_HOURS:
                    continue  # 退单超出24小时窗口，跳过

                # 检查地区和金额是否匹配
                if return_region == sale_region and return_amount == sale_amount:
                    has_return = True
                    logging.info(f"找到匹配退单: 销售时间={sale_time}, 销售金额={sale_amount}, 退单时间={return_time}")
                    break  # 找到匹配退单后跳出循环

            # 如果没有找到匹配的退单，则计入高光数据
            if not has_return:
                valid_highlights.append(sale_amount)
                logging.info(f"有效销售: 地区={sale_region}, 金额={sale_amount}, 时间={sale_time}, 是否高光=True")

        # 打印所有有效高光数据
        logging.info(f"有效高光数据: {valid_highlights}")

        # 排序并获取全部高光数据（降序排序，用于高亮显示）
        self.highlighted_values = sorted(valid_highlights, reverse=True)

        # 创建一个降序排序的高光数据列表，用于展示
        self.sorted_highlights = sorted(valid_highlights, reverse=True)

        # 获取高光数据前十
        self.top_ten_highlights = self.highlighted_values[:10] if len(self.highlighted_values) >= 10 else self.highlighted_values

    def _calculate_top_forty_stats(self):
        """计算高光数据的统计信息"""
        # 使用highlight_num限制高光数据数量
        data = self.highlighted_values[:self.highlight_num] if len(self.highlighted_values) > self.highlight_num else self.highlighted_values

        if not data:
            self.top_forty_stats = {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
            return

        # 计算总和并除以highlight_num来获得正确的平均值
        total_sum = sum(data)
        mean_value = total_sum / self.highlight_num if self.highlight_num > 0 else 0

        # 使用内置函数计算统计值
        sorted_data = sorted(data)
        n = len(sorted_data)

        if n == 0:
            median = 0
        elif n % 2 == 0:
            median = (sorted_data[n//2 - 1] + sorted_data[n//2]) / 2
        else:
            median = sorted_data[n//2]

        self.top_forty_stats = {
            'min': min(data) if data else 0,
            'max': max(data) if data else 0,
            'median': float(median),
            'mean': round(mean_value, 2)
        }

    def _calculate_daily_stats(self, simulated_now=None):
        """计算24小时内的销售统计"""
        now = simulated_now or datetime.now()
        twenty_four_hours_ago = now - TWENTY_FOUR_HOURS

        # 过滤24小时内的记录
        daily_records = [
            record for record in self.display_data
            if record['datetime'] >= twenty_four_hours_ago
        ]

        total_sales = 0
        valid_orders = 0
        return_orders = 0

        for record in daily_records:
            if record['delta'] > 0:
                total_sales += record['delta']
                valid_orders += 1
            else:
                return_orders += 1

        self.daily_stats = {
            'total_sales': total_sales,
            'valid_orders': valid_orders,
            'return_orders': return_orders,
            'net_growth': total_sales - sum(abs(record['delta']) for record in daily_records if record['delta'] < 0)
        }

    def _calculate_region_stats(self):
        """计算各地区的销售统计"""
        region_totals = {region: 0 for region in order}
        region_counts = {region: 0 for region in order}

        # 计算各地区的总销量和订单数
        for record in self.display_data:
            region = record['region']
            if record['delta'] > 0:  # 只计算正向销量
                region_totals[region] += record['delta']
                region_counts[region] += 1

        # 计算总销量
        total_sales = sum(region_totals.values())

        # 格式化统计数据
        self.region_stats = {
            region: {
                'total': region_totals[region],
                'percentage': round((region_totals[region] / total_sales) * 100, 2) if total_sales > 0 else 0,
                'count': region_counts[region]
            }
            for region in order
        }


def process_request(file_key):
    try:
        file_path = file_paths.get(file_key)
        if not file_path or not os.path.exists(file_path):
            return f"文件 {file_key} 不存在", 404

        # 根据不同的 file_key 设置不同的 highlight_num
        if file_key in ['k4jisung', 'k4mark', 'somi', 'suhua', 'miyeon','k4jaemin','k4jeno']:
            highlight_num = 30
        elif file_key in ['chanyeolday1','chanyeolday2']:  # 修正了拼写错误，从aepsa改为aespa
            highlight_num = 400
        else:
            highlight_num = 30

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f if line.strip()]

        processor = SalesProcessor(highlight_num=highlight_num)
        processor.process_file(lines)

        # 处理计算请求
        calculation_result = None
        if request.method == 'POST' and 'divisor' in request.form:
            try:
                divisor = float(request.form['divisor'])
                if divisor != 0:
                    calculation_result = processor.latest_total / divisor
            except (ValueError, ZeroDivisionError):
                calculation_result = "无效输入"

        return render_template_string(HTML_TEMPLATE,
                                      latest_total=processor.latest_total,
                                      top_forty_stats=processor.top_forty_stats,
                                      display_data=processor.display_data,
                                      highlighted_values=processor.highlighted_values,
                                      highlight_num=highlight_num,
                                      daily_stats=processor.daily_stats,
                                      region_stats=processor.region_stats,
                                      calculation_result=calculation_result,
                                      total_records=len(processor.display_data),
                                      top_ten_highlights=processor.top_ten_highlights,
                                      sorted_highlights=processor.sorted_highlights)

    except Exception as e:
        logging.error(f"请求处理失败: {str(e)}")
        return f"服务器错误: {str(e)}", 500


# @app.route('/allday.py', methods=['GET', 'POST'])
# def allday_monitor():
#     file_key = 'allday.py'
#     return process_request(file_key)
#
# @app.route('/aespa', methods=['GET', 'POST'])
# def aespa_monitor():
#     file_key = 'aespa'
#     return process_request(file_key)
#
# @app.route('/k4jaehee01', methods=['GET', 'POST'])
# def k4jaehee_monitor():
#     file_key = 'k4jaehee'
#     return process_request(file_key)

@app.route('/chanyeolday1', methods=['GET', 'POST'])
def chanyeolday1_monitor():
    file_key = 'chanyeolday1'
    return process_request(file_key)

@app.route('/chanyeolday2', methods=['GET', 'POST'])
def chanyeolday2_monitor():
    file_key = 'chanyeolday2'
    return process_request(file_key)

@app.route('/somi', methods=['GET', 'POST'])
def somi_monitor():
    file_key = 'somi'
    return process_request(file_key)

@app.route('/jeno', methods=['GET', 'POST'])
def jeno_monitor():
    file_key = 'k4jeno'
    return process_request(file_key)

@app.route('/chenle', methods=['GET', 'POST'])
def chenle_monitor():
    file_key = 'k4chenle'
    return process_request(file_key)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=1718, debug=True)