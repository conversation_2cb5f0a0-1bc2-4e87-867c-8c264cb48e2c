from flask import Flask, render_template_string, request, session
import os
import numpy as np
import logging
import time
from datetime import datetime
from collections import deque

# 定义库存处理类
class InventoryProcessor:
    # --- 新增: 为日期解析增加缓存，避免重复调用 ---
    parse_time_cache = {}

    def __init__(self, threshold=None, highlight_num=40, is_ph_file=False, is_ma=False):
        self.threshold = threshold if threshold is not None else (500 if is_ma else 540 if is_ph_file else 1101)
        self.highlight_num = highlight_num
        self.is_ph_file = is_ph_file
        self.is_ma = is_ma

    @staticmethod
    def parse_time(time_str):
        """带有简单缓存的时间解析函数，加速大量重复解析"""
        time_str = time_str.strip().rstrip(':')
        cached = InventoryProcessor.parse_time_cache.get(time_str)
        if cached is not None:
            return cached

        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"]:
            try:
                dt = datetime.strptime(time_str, fmt)
                InventoryProcessor.parse_time_cache[time_str] = dt
                return dt
            except ValueError:
                continue

        logging.error(f"无法解析日期时间字符串: {time_str}")
        dt = datetime.min
        InventoryProcessor.parse_time_cache[time_str] = dt
        return dt

    def process_queue(self, raw_value, pending, valid, time_window, current_ts):
        """优化: 仅解析一次当前行时间，并在记录中缓存 dt，减少循环中的解析开销"""
        processed_value = int(round(raw_value))
        current_dt = self.parse_time(current_ts)

        if processed_value < 0:  # 销量记录
            qty = abs(processed_value)
            sale_record = {
                'timestamp': current_ts,
                'timestamp_dt': current_dt,  # 缓存 datetime 对象
                'original': qty,
                'remaining': qty,
                'valid': qty <= self.threshold
            }
            pending.append(sale_record)
            if sale_record['valid']:
                valid.append(qty)
            return qty
        else:  # 退款记录
            refund = abs(processed_value)
            for i in range(len(pending) - 1, -1, -1):
                record = pending[i]
                time_diff = (current_dt - record['timestamp_dt']).total_seconds()
                if time_diff <= time_window and record['valid'] and record['original'] == refund:
                    try:
                        valid.remove(refund)
                    except ValueError:
                        pass
                    del pending[i]
                    return refund
            return 0

    def process_data(self, data):
        processed_data = []
        app_pending = deque()
        shop_pending = deque()
        app_valid = []
        shop_valid = []
        total_sales = 0
        app_sales = 0

        for line in data:
            if not line.strip():
                continue
            parts = line.split(',')
            if len(parts) < 5:
                continue
            try:
                ts = parts[0].strip()
                raw_sold = float(parts[2])
                raw_stock = float(parts[4])

                # 筛选并计算总销量
                if abs(raw_sold) < self.threshold:
                    total_sales += -raw_sold
                if abs(raw_stock) < self.threshold:
                    total_sales += -raw_stock

                # 计算 app 端的销量
                if abs(raw_sold) <= self.threshold:
                    if raw_sold < 0:
                        app_sales += abs(raw_sold)
                    else:
                        app_sales -= raw_sold

                self.process_queue(raw_sold, app_pending, app_valid, 1920, ts)
                self.process_queue(raw_stock, shop_pending, shop_valid, 1020, ts)
                filtered = abs(raw_sold) > self.threshold or abs(raw_stock) > self.threshold
                processed_data.append((line, filtered, raw_sold, raw_stock))
            except Exception as e:
                logging.error(f"数据处理失败: {line} - {str(e)}")
                continue

        combined = sorted(app_valid + shop_valid, reverse=True)[:self.highlight_num]
        # 时间倒序排列（最新数据在前）
        processed_data.reverse()
        return processed_data, combined, total_sales, app_sales


    def get_paginated_data(self, full_processed_data, page=1, page_size=100):
        total_items = len(full_processed_data)
        total_pages = (total_items + page_size - 1) // page_size if total_items > 0 else 1
        page = max(1, min(page, total_pages))
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_items)
        return full_processed_data[start_idx:end_idx], total_pages

    @staticmethod
    def calculate_stats(values):
        if not values:
            return {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
        arr = np.array(values)
        return {
            'min': int(np.min(arr).item()),
            'max': int(np.max(arr).item()),
            'median': float(np.median(arr).item()),
            'mean': round(float(np.mean(arr)), 2)
        }

# 配置日志记录
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'  # 请替换为安全的密钥

@app.context_processor
def utility_processor():
    def now():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return dict(abs=abs, enumerate=enumerate, now=now)

file_paths = {
    'yy':'yy.txt',
    'kun':'kun.txt',
    'xj':'xj.txt',
    'ten':'ten.txt',
    'hen':'hen.txt',
    'phyy':'phyy.txt',
    'phkun':'phkun.txt',
    'phxj':'phxj.txt',
    'phten':'phten.txt',
    'phhen':'phhen.txt',
    'wayv':'wayv.txt',
    'phhyeonbin':'phhyeonbin.txt',
    'soobin':'soobin.txt',
    'yeonjun':'yeonjun.txt',
    'beomgyu':'beomgyu.txt',
    'taehyun':'taehyun.txt',
    'huenigkai':'huenigkai.txt'
}

# 更新模板，添加新功能和响应式设计
template = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐶 小狗包数据</title>
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
    /* 全局样式 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
        background: #f0f8ff; /* 浅蓝色背景 */
        line-height: 1.6;
        padding: 8px;
    }

    /* 标题样式 - 增加帕恰狗元素 */
    .header {
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #4a90e2, #5fa8ff); /* 蓝色渐变 */
        color: white;
        margin-bottom: 10px;
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }

    .header::before {
        content: "🐶";
        position: absolute;
        font-size: 60px;
        opacity: 0.2;
        right: 20px;
        top: 10px;
    }

    .header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* 按钮容器 */
    .button-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 6px;
        margin: 10px 0;
    }

    .action-button {
        padding: 8px 12px;
        background: #4a90e2; /* 蓝色按钮 */
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 0.85rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .action-button:hover {
        background: #3a80d2;
        transform: translateY(-2px);
    }

    /* 数据容器 */
    .data-container {
        background: white;
        padding: 15px;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        margin-bottom: 10px;
    }

    /* 卡片式布局 */
    .dashboard-card {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    @media (max-width: 768px) {
        .dashboard-card {
            grid-template-columns: 1fr;
        }
    }

    .stats-section, .highlight-section {
        background: #f8fbff;
        border-radius: 12px;
        padding: 15px;
        border: 1px solid #e1f0ff;
    }

    /* 统计信息 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }

    .stat-item {
        background: #e6f2ff;
        padding: 12px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .stat-item h3 {
        color: #2c6db3;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .stat-value {
        font-size: 1.3rem;
        font-weight: bold;
        color: #1a5cb0;
    }

    /* 高光数据展示 */
    .highlight-title {
        color: #2c6db3;
        font-size: 1.1rem;
        margin-bottom: 12px;
        text-align: center;
        padding-bottom: 8px;
        border-bottom: 2px dashed #d1e6ff;
    }

    .highlight-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(42px, 1fr));
        gap: 5px;
        justify-content: center;
    }

    .highlight-item {
        height: 36px;
        background: #e6f2ff;
        color: #1a5cb0;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.2s;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .highlight-item:hover {
        transform: scale(1.05);
        background: #d1e6ff;
    }

    .section-title {
        grid-column: 1 / -1;
        text-align: center;
        margin: 8px 0;
        color: #4a90e2;
        font-size: 0.95rem;
        font-weight: 600;
    }

    /* 设置表单 - 单行布局 */
    .settings-form {
        background: #f8fbff;
        border-radius: 12px;
        padding: 15px;
        margin: 15px 0;
        border: 1px solid #e1f0ff;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr auto;
        gap: 10px;
        align-items: end;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group label {
        margin-bottom: 6px;
        font-size: 0.9rem;
        color: #4a7ab0;
        font-weight: 500;
    }

    .form-input {
        width: 100%;
        padding: 10px;
        border: 1px solid #c2d9ff;
        border-radius: 8px;
        font-size: 0.95rem;
        background: #f0f8ff;
    }

    .form-button {
        padding: 10px 15px;
        background: #4a90e2;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.2s;
    }

    .form-button:hover {
        background: #3a80d2;
        transform: translateY(-2px);
    }

    /* 工具栏 */
    .toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 15px 0;
        justify-content: center;
    }

    .toolbar-item {
        display: flex;
        align-items: center;
        background: #e6f2ff;
        padding: 8px 15px;
        border-radius: 30px;
    }

    .toolbar-button {
        padding: 8px 15px;
        background: #4a90e2;
        color: white;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .toolbar-button:hover {
        background: #3a80d2;
        transform: translateY(-2px);
    }

    /* 表格容器 */
    .table-container {
        overflow-x: auto;
        margin-top: 15px;
        border: 1px solid #e1f0ff;
        border-radius: 12px;
        background: white;
    }

    /* 表格样式 */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        min-width: 600px;
    }

    .data-table th {
        background: #4a90e2;
        color: white;
        padding: 12px;
        text-align: center;
        position: sticky;
        top: 0;
    }

    .data-table td {
        padding: 10px;
        text-align: center;
        border-bottom: 1px solid #e1f0ff;
    }

    .data-table tr:nth-child(even) {
        background: #f8fbff;
    }

    /* 状态颜色 */
    .valid { background: #e8f5e9; }
    .invalid { background: #ffebee; }
    .normal { background: #e3f2fd; }
    .positive-change { background: #fff3e0; }

    /* 分页控件 */
    .pagination-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        align-items: center;
        margin-top: 15px;
        padding: 10px;
        background: #f8fbff;
        border-radius: 12px;
    }

    /* 弹窗样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 25px;
        border-radius: 15px;
        width: 90%;
        max-width: 500px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        position: relative;
    }

    .close {
        color: #aaa;
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    /* 页脚 */
    footer {
        text-align: center;
        padding: 15px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .header h1 {
            font-size: 1.3rem;
        }
        
        .action-button {
            padding: 8px;
            font-size: 0.8rem;
        }
        
        .stat-value {
            font-size: 1.1rem;
        }
        
        .highlight-item {
            height: 32px;
            font-size: 0.8rem;
        }
        
        .form-group {
            width: 100%;
        }
    }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐶 小狗包数据签售网站</h1>
    </div>

    <div class="button-group">
        <form method="post">
            {% for name in buttons %}
            <button type="submit" name="{{ name }}" class="action-button">
                {{ name|capitalize }}
            </button>
            {% endfor %}
        </form>
    </div>

    <div class="data-container">
        <!-- 数据仪表盘卡片 -->
        <div class="dashboard-card">
            <div class="stats-section">
                <h2 class="highlight-title"> 数据统计</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3>总销量</h3>
                        <div class="stat-value">{% if file_name in ['sign','photo'] %}{{ new_total }}{% else %}{{ total }}{% endif %}</div>
                    </div>
                    <div class="stat-item">
                        <h3>APP（韩国）销量</h3>
                        <div class="stat-value">{{ kms_total }}</div>
                    </div>
                    {% if stats is not none %}
                    <div class="stat-item">
                        <h3>最小值</h3>
                        <div class="stat-value">{{ stats.min }}</div>
                    </div>
                    <div class="stat-item">
                        <h3>最大值</h3>
                        <div class="stat-value">{{ stats.max }}</div>
                    </div>
                    <div class="stat-item">
                        <h3>中位数</h3>
                        <div class="stat-value">{{ stats.median }}</div>
                    </div>
                    <div class="stat-item">
                        <h3>平均值</h3>
                        <div class="stat-value">{{ stats.mean }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="highlight-section">
                <h2 class="highlight-title">高光数据 (TOP {{ highlight_num }})</h2>
                <div class="highlight-grid">
                    {# 高光数据展示逻辑保持不变 #}
                    {% if file_name in ['yy','kun','xj','ten','hen','soobin','yeonjun','beomgyu','taehyun','huenigkai','seulgi','irene','alldayproject','kun','yy','ten'] %}
                    <div class="section-title">1-15</div>
                    {% for i, value in enumerate(highlighted_values) if i < 15 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">16-30</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 15 and i < 30 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['签名应募','ahof'] %}
                    <div class="section-title">1-50</div>
                    {% for i, value in enumerate(highlighted_values) if i < 50 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">51-200</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 50 and i < 200 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['phhyeonbin','phchihen','phdaisuke','phhan','phjeongwoo','phjl','phjuwon','phshuaibo','phsteven','phwoongki','phhen','phyy','phxj','phkun','phten'] %}
                    <div class="section-title">1-5</div>
                    {% for i, value in enumerate(highlighted_values) if i <7 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">6-7</div>
                    {% for i, value in enumerate(highlighted_values) if i >=8 and i < 15 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['sion','riku','yushi','jaehee','ryo','sakuya'] %}
                    <div class="section-title">1-25</div>
                    {% for i, value in enumerate(highlighted_values) if i < 25 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">26-40</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 25 and i < 40 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['wayv'] %}
                    <div class="section-title">125-225</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 125 and i < 225 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['yenasz', 'yenagz'] %}
                    <div class="section-title">1-30</div>
                    {% for i, value in enumerate(highlighted_values) if i < 30 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">31-100</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 30 and i < 100 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% elif file_name in ['phyenasz', 'phyenagz'] %}
                    <div class="section-title">1-5</div>
                    {% for i, value in enumerate(highlighted_values) if i < 5 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">6-10</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 5 and i < 10 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    
                    {% else %}
                    <div class="section-title">1-125</div>
                    {% for i, value in enumerate(highlighted_values) if i < 125 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    <div class="section-title">126-225</div>
                    {% for i, value in enumerate(highlighted_values) if i >= 125 and i < 225 %}
                        <div class="highlight-item">{{ value }}</div>
                    {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 设置表单 - 单行布局 -->
        <div class="settings-form">
            <form method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label for="threshold">注水值</label>
                        <input type="number" name="threshold" id="threshold" class="form-input" 
                               placeholder="默认: {{ default_threshold }}" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="highlight_num">抓取个数</label>
                        <input type="number" name="highlight_num" id="highlight_num" class="form-input" 
                               placeholder="默认: {{ default_highlight_num }}" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="cut_value">计算除数</label>
                        <input type="number" name="cut_value" id="cut_value" class="form-input" 
                               step="0.1" min="0" placeholder="输入除数">
                    </div>
                    
                    <button type="submit" class="form-button">设置</button>
                </div>
            </form>
        </div>

        {% if cut is not none %}
        <div class="stat-item" style="background: #fff9e6; margin: 15px 0; text-align: center;">
            <h3>计算结果</h3>
            <div class="stat-value" style="font-size: 1.5rem;">{{ cut }}</div>
        </div>
        {% endif %}
        
        <div class="toolbar">
            <div class="toolbar-item">
                <input type="checkbox" id="autoRefresh" style="margin-right: 6px;">
                <label for="autoRefresh" style="font-size: 0.95rem; color: #4a7ab0;">自动刷新</label>
            </div>
            <button type="button" id="exportExcel" class="toolbar-button">
                <span>📤 导出Excel</span>
            </button>
            <button type="button" id="helpButton" class="toolbar-button">
                <span>❓ 说明</span>
            </button>
        </div>

        <h3 style="color: #2c6db3; margin: 15px 0 10px;">数据表格</h3>
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>变化时间</th>
                        <th>APP（韩国）库存</th>
                        <th>APP（韩国）变化</th>
                        <th>微店（中国）库存</th>
                        <th>微店（中国）变化</th>
                    </tr>
                </thead>
                <tbody>
                    {% for line, filtered, real_sold, real_stock in data %}
                    {% set parts = line.split(',') %}
                    <tr class="{% if real_sold > 0 and real_stock > 0 %}positive-change{% elif not filtered %}normal{% else %}valid{% endif %}">
                        <td>{{ parts[0] }}</td>
                        <td>{{ parts[1] }}</td>
                        <td>{{ parts[2] }}</td>
                        <td>{{ parts[3] }}</td>
                        <td>{{ parts[4] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="pagination-controls">
            <form method="post" id="paginationForm">
                <input type="hidden" name="cut_value" value="{{ request.form.get('cut_value', '') }}">
                <input type="hidden" name="threshold" value="{{ request.form.get('threshold', '') }}">
                <input type="hidden" name="highlight_num" value="{{ request.form.get('highlight_num', '') }}">
                
                <button type="submit" name="page" value="{{ page - 1 }}" class="action-button" {% if page <= 1 %}disabled{% endif %}>上一页</button>
                <span style="color: #4a7ab0; font-weight: 500;">第 {{ page }} / {{ total_pages }} 页</span>
                <button type="submit" name="page" value="{{ page + 1 }}" class="action-button" {% if page >= total_pages %}disabled{% endif %}>下一页</button>
                
                <input type="number" name="page" min="1" max="{{ total_pages }}" value="{{ page }}" class="form-input" style="width:90px; padding:8px;">
                <select name="page_size" onchange="this.form.submit()" class="form-input" style="padding:8px;">
                    {% for size in [50, 100, 200, 500, 1000, 2000] %}
                    <option value="{{ size }}" {% if page_size == size %}selected{% endif %}>每页{{ size }}条</option>
                    {% endfor %}
                </select>
            </form>
        </div>
    </div>
    
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3 style="color: #4a90e2; margin-bottom: 15px;">平台使用说明</h3>
            <p>🔄 <strong>自动刷新</strong>：开启后会自动刷新数据</p>
        </div>
    </div>

    <footer>
        <p>数据更新时间: {{ now() }}</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 弹窗功能
            var modal = document.getElementById('helpModal');
            var btn = document.getElementById('helpButton');
            var span = document.getElementsByClassName('close')[0];
            
            btn.onclick = function() {
                modal.style.display = "block";
            }
            
            span.onclick = function() {
                modal.style.display = "none";
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // 自动刷新功能
            var autoRefreshCheckbox = document.getElementById('autoRefresh');
            var refreshIntervalId = null;
            
            if (autoRefreshCheckbox) {
                var currentRoute = window.location.pathname;
                var storageKey = 'autoRefreshEnabled_' + currentRoute;
                
                var savedState = localStorage.getItem(storageKey);
                if (savedState === 'true') {
                    autoRefreshCheckbox.checked = true;
                    startAutoRefresh();
                }
                
                autoRefreshCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                        localStorage.setItem(storageKey, 'true');
                    } else {
                        stopAutoRefresh();
                        localStorage.setItem(storageKey, 'false');
                    }
                });
                
                window.addEventListener('beforeunload', function() {
                    if (refreshIntervalId !== null) {
                        clearInterval(refreshIntervalId);
                    }
                    localStorage.setItem(storageKey, autoRefreshCheckbox.checked ? 'true' : 'false');
                });
            }
            
            function startAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                }
                
                var currentFileName = document.getElementById('currentFileName')?.value || '';
                var buttons = document.querySelectorAll('form button[type="submit"]');
                var currentButton = null;
                
                for (var i = 0; i < buttons.length; i++) {
                    if (buttons[i].name === currentFileName) {
                        currentButton = buttons[i];
                        break;
                    }
                }
                
                if (currentButton) {
                    refreshIntervalId = setInterval(function() {
                        currentButton.click();
                    }, 5000); // 每5秒刷新一次
                }
            }
            
            function stopAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                    refreshIntervalId = null;
                }
            }
            
            // 导出Excel功能
            var exportBtn = document.getElementById('exportExcel');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    exportToExcel();
                });
            }
            
            function exportToExcel() {
                try {
                    const wb = XLSX.utils.book_new();
                    const currentFileName = document.getElementById('currentFileName')?.value || '小狗包数据';
                    
                    const tableHeaders = Array.from(document.querySelectorAll('.data-table th'))
                        .map(th => th.textContent.trim());
                    
                    const tableRows = [];
                    document.querySelectorAll('.data-table tbody tr').forEach(tr => {
                        const row = Array.from(tr.querySelectorAll('td'))
                            .map(td => td.textContent.trim());
                        tableRows.push(row);
                    });
                    
                    const ws = XLSX.utils.aoa_to_sheet([tableHeaders, ...tableRows]);
                    XLSX.utils.book_append_sheet(wb, ws, currentFileName + "数据");
                    
                    const fileName = currentFileName + '_数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    XLSX.writeFile(wb, fileName);
                    
                    alert('导出成功!');
                } catch (error) {
                    console.error('导出失败:', error);
                    alert('导出错误: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
'''


def parse_time(time_str):
    return InventoryProcessor.parse_time(time_str)


_file_lookup_cache = {}


def _resolve_file_path(file_path: str) -> str:
    """若给定路径不存在，则在当前工作目录递归搜索同名文件，并返回首个匹配绝对路径。"""
    # 直接存在就返回
    if os.path.isfile(file_path):
        return file_path

    # 尝试缓存
    base_name = os.path.basename(file_path)
    cached = _file_lookup_cache.get(base_name)
    if cached and os.path.isfile(cached):
        return cached

    # 递归遍历（深度优先）
    for root, _dirs, files in os.walk('.', topdown=True):
        if base_name in files:
            full = os.path.join(root, base_name)
            _file_lookup_cache[base_name] = full
            return full

    # 未找到，返回原值，后续逻辑会捕获异常
    return file_path


def read_file(file_path):
    # 尝试解析真实路径
    resolved_path = _resolve_file_path(file_path)

    if not os.path.isfile(resolved_path):
        logging.error(f"文件不存在: {file_path}")
        return []
    try:
        mtime = os.path.getmtime(resolved_path)
        if (resolved_path, mtime) in read_file.cache:
            return read_file.cache[(resolved_path, mtime)]
        with open(resolved_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = [line.strip() for line in f if line.strip()]
            # 时间倒序排列（最新数据在前）
            lines.sort(key=lambda x: parse_time(x.split(',')[0]), reverse=True)
            read_file.cache = {(resolved_path, mtime): lines}
            return lines
    except Exception as e:
        logging.error(f"文件读取失败: {e}")
        return []


read_file.cache = {}

# 新增全量数据缓存，避免重复计算
full_data_processing_cache = {}


# 找到 process_request 函数
def process_request(buttons, default_file, highlight_num_override=None, start=0, end=None):
    # --- 记录开始时间，用于统计载入/处理耗时 ---
    start_time_perf = time.perf_counter()

    # 选按钮
    selected = next((k for k in buttons if k in request.form), None)
    session['current_file'] = file_paths.get(selected, session.get('current_file', default_file))
    file_to_show = session['current_file']

    # 获取分页参数（新增）
    try:
        page = int(request.form.get('page', request.args.get('page', 1)))
    except ValueError:
        page = 1
    try:
        page_size = int(request.form.get('page_size', request.args.get('page_size', 100)))
    except ValueError:
        page_size = 100

    # === 读取文件并计算阈值、高光数等（新增，必须放在缓存逻辑之前） ===
    # 解析真实路径，避免后续 getmtime 失败
    resolved_path = _resolve_file_path(file_to_show)

    data_lines = read_file(resolved_path)

    # 文件类型特征
    is_ph = 'ph' in file_to_show.lower()
    is_ma = any(kw in file_to_show.lower() for kw in ['ma', 'mark'])

    # 获取当前显示的文件名（去掉扩展名）
    file_name = next((k for k, v in file_paths.items() if v == file_to_show), None)
    if not file_name:
        file_name = os.path.basename(file_to_show).split('.')[0]

    # 用户自定义阈值
    try:
        threshold = int(request.form.get('threshold')) if request.form.get('threshold') else None
    except Exception:
        threshold = None

    # 计算默认阈值
    default_threshold = 500 if is_ma else 540 if is_ph else 1101

    # 计算默认高光数量
    if file_name in ['sion','riku','yushi','jaehee','ryo','sakuya','pow']:
        default_hn = 40
    elif file_name in ['phsion','phriku','phyushi','phjaehee','phryo','phsakuya','yuqi','shuhua','minnie','miyeon','soyeon']:
        default_hn = 15
    elif file_name in ['phhyeonbin','phchihen','phdaisuke','phhan','phjeongwoo','phjl','phjuwon','phshuaibo','phsteven','phwoongki','phhen','phyy','phxj','phkun','phten']:
        default_hn = 7
    elif file_name == 'photo':
        default_hn = 60
    elif file_name in ['yenasz', 'yenagz']:
        default_hn = 100
    elif file_name == 'coffee':
        default_hn = 50
    elif file_name in ['mark', 'renjun', 'jaemin', 'haechan', 'chenle', 'jisung', 'jeno']:
        default_hn = 40
    elif is_ph:
        default_hn = 225
    elif file_name in ['yy','kun','xj','ten','hen','soobin','yeonjun','beomgyu','taehyun','huenigkai','seulgi','irene','alldayproject']:
        default_hn = 30
    elif file_name in ['签名应募', 'ahof']:
        default_hn = 200
    else:
        default_hn = 225

    # 最终高光数量
    if highlight_num_override is not None:
        highlight_num = highlight_num_override
    elif request.form.get('highlight_num'):
        try:
            highlight_num = int(request.form['highlight_num'])
        except ValueError:
            highlight_num = default_hn
    else:
        highlight_num = default_hn

    # 创建数据处理器实例
    processor = InventoryProcessor(threshold, highlight_num, is_ph, is_ma)

    # === 下面是缓存键与全量数据处理逻辑（保持不变） ===
    # 构建全量数据缓存键（新增）
    full_data_cache_key = (
        resolved_path,
        threshold,
        highlight_num,
        is_ph,
        is_ma,
        os.path.getmtime(resolved_path)
    )

    # 全量数据处理 & 缓存（新增）
    if full_data_cache_key in full_data_processing_cache:
        full_processed, highlighted, total, kms_total = full_data_processing_cache[full_data_cache_key]
    else:
        full_processed, highlighted, total, kms_total = processor.process_data(data_lines)
        full_data_processing_cache[full_data_cache_key] = (full_processed, highlighted, total, kms_total)
        if len(full_data_processing_cache) > 10:
            full_data_processing_cache.pop(next(iter(full_data_processing_cache)))


    paginated_data, total_pages = processor.get_paginated_data(full_processed, page=page, page_size=page_size)

    highlighted_values = highlighted or []

    # 初始化所有统计变量
    stats = InventoryProcessor.calculate_stats(highlighted)
    stats_1_25 = None
    stats_26_40 = None
    stats_1_5 = None
    stats_6_10 = None
    stats_1_50 = None
    stats_51_250 = None
    stats_1_30 = None
    stats_31_100 = None

    # 根据文件名处理不同的统计数据
    if file_name in ['sion','riku','yushi','jaehee','ryo','sakuya']:
        # 计算1-25阶段统计
        if len(highlighted) >= 25:
            stats_1_25 = InventoryProcessor.calculate_stats(highlighted[:25])
        else:
            stats_1_25 = InventoryProcessor.calculate_stats(highlighted)

        # 计算26-40阶段统计
        if len(highlighted) >= 25:
            stats_26_40 = InventoryProcessor.calculate_stats(highlighted[25:40])
        elif len(highlighted) > 40:
            stats_26_40 = InventoryProcessor.calculate_stats(highlighted[25:])

    # 为yenasz和yenagz添加统计数据
    elif file_name in ['yenasz', 'yenagz']:
        # 计算1-30阶段统计
        if len(highlighted) >= 30:
            stats_1_30 = InventoryProcessor.calculate_stats(highlighted[:30])
        else:
            stats_1_30 = InventoryProcessor.calculate_stats(highlighted)

        # 计算31-100阶段统计
        if len(highlighted) >= 30:
            stats_31_100 = InventoryProcessor.calculate_stats(highlighted[30:100])
        elif len(highlighted) > 30:
            stats_31_100 = InventoryProcessor.calculate_stats(highlighted[30:])

    # 为phyenasz和phyenagz添加统计数据
    elif file_name in ['phyenasz', 'phyenagz']:
        # 计算1-5阶段统计
        if len(highlighted) >= 5:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted[:5])
        else:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted)

        # 计算6-10阶段统计
        if len(highlighted) >= 10:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:10])
        elif len(highlighted) > 5:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:])

    elif file_name in ['phhyeonbin','phchihen','phdaisuke','phhan','phjeongwoo','phjl','phjuwon','phshuaibo','phsteven','phwoongki','phhen','phyy','phxj','phkun','phten']:
        # 计算1-5阶段统计
        if len(highlighted) >= 5:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted[:5])
        else:
            stats_1_5 = InventoryProcessor.calculate_stats(highlighted)

        # 计算6-10阶段统计
        if len(highlighted) >= 7:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:7])
        elif len(highlighted) > 5:
            stats_6_10 = InventoryProcessor.calculate_stats(highlighted[5:])

    elif file_name in ['yenasz','yenagz']:
        # 计算1-60阶段统计
        if len(highlighted) >= 30:
            stats_1_50 = InventoryProcessor.calculate_stats(highlighted[:30])
        else:
            stats_1_50 = InventoryProcessor.calculate_stats(highlighted)

        # 计算61-260阶段统计
        if len(highlighted) >= 100:
            stats_51_250 = InventoryProcessor.calculate_stats(highlighted[30:100])
        elif len(highlighted) > 30:
            stats_51_250 = InventoryProcessor.calculate_stats(highlighted[30:])

    # 计算总销量
    if file_name in ['suhua','meiyan']:
        new_total = total + 210
    elif file_name == 'riize':
        new_total = total + 1
    elif file_name == '1':
        new_total = total + 1880
    else:
        new_total = total


    if file_name != 'sign' and start is not None and end is not None and paginated_data:
        paginated_data = paginated_data[start:end]

    # 除法计算
    cut = None
    if 'cut_value' in request.form:
        try:
            dv = float(request.form['cut_value'])
            # 针对 sign 和 photo 数据，使用包含额外销量的总销量进行计算
            if file_name == 'sign' or file_name == 'photo':
                cut = new_total / dv if dv != 0 else "除零错误"
            else:
                cut = total / dv if dv != 0 else "除零错误"
        except:
            cut = "无效输入"

    # --- 计算并传递载入时间 ---
    load_time_ms = int((time.perf_counter() - start_time_perf) * 1000)

    return render_template_string(template,
                                  buttons=buttons,
                                  data=paginated_data,  # 使用分页后的数据
                                  highlighted_values=highlighted_values,
                                  stats=stats,
                                  total=int(total),
                                  kms_total=int(kms_total),
                                  highlight_num=highlight_num,
                                  cut=cut,
                                  file_name=file_name,
                                  stats_1_25=stats_1_25,
                                  stats_26_40=stats_26_40,
                                  stats_1_5=stats_1_5,
                                  stats_6_10=stats_6_10,
                                  stats_1_30=stats_1_30,
                                  stats_31_100=stats_31_100,
                                  stats_1_50=stats_1_50,
                                  stats_51_250=stats_51_250,
                                  start=start,
                                  end=end,
                                  new_total=new_total,
                                  # 新增: 默认值显示
                                  default_threshold=default_threshold,
                                  default_highlight_num=default_hn,
                                  # 新增: 载入时间 (毫秒)
                                  load_time=load_time_ms,
                                  page=page,
                                  total_pages=total_pages,
                                  page_size=page_size)

@app.route('/xgb018', methods=['GET', 'POST'])
def xgb018_data():
    buttons = list(file_paths.keys())
    return process_request(buttons, 'phlite.txt')

@app.route('/soobin11', methods=['GET', 'POST'])
def soobin_data():
    return process_request(['soobin'], 'soobin.txt')

@app.route('/yeonjun21', methods=['GET', 'POST'])
def yeonjun_data():
    return process_request(['yeonjun'], 'yeonjun.txt')

@app.route('/beomgyu33', methods=['GET', 'POST'])
def beomgyu_data():
    return process_request(['beomgyu'], 'beomgyu.txt')

@app.route('/taehyun43', methods=['GET', 'POST'])
def taehyun_data():
    return process_request(['taehyun'], 'taehyun.txt')

@app.route('/huenigkai55', methods=['GET', 'POST'])
def huenigkai_data():
    return process_request(['huenigkai'], 'huenigkai.txt')
#

@app.route('/kun76', methods=['GET', 'POST'])
def kun_data():
    if request.method == 'GET':
        session['current_file'] = 'kun.txt'  # Explicitly set default file for this route
    return process_request(['kun'], 'kun.txt')

@app.route('/phkun29', methods=['GET', 'POST'])
def phkun_data():
    if request.method == 'GET':
        session['current_file'] = 'phkun.txt'  # Explicitly set default file for this route
    return process_request(['phkun'], 'phkun.txt')

@app.route('/ten83', methods=['GET', 'POST'])
def ten_data():
    if request.method == 'GET':
        session['current_file'] = 'ten.txt'  # Explicitly set default file for this route
    return process_request(['ten'], 'ten.txt')

@app.route('/phten47', methods=['GET', 'POST'])
def phten_data():
    if request.method == 'GET':
        session['current_file'] = 'phten.txt'  # Explicitly set default file for this route
    return process_request(['phten'], 'phten.txt')

@app.route('/xj15', methods=['GET', 'POST'])
def xj_data():
    if request.method == 'GET':
        session['current_file'] = 'xj.txt'  # Explicitly set default file for this route
    return process_request(['xj'], 'xj.txt')

@app.route('/phxj62', methods=['GET', 'POST'])
def phxj_data():
    if request.method == 'GET':
        session['current_file'] = 'phxj.txt'  # Explicitly set default file for this route
    return process_request(['phxj'], 'phxj.txt')

@app.route('/yy94', methods=['GET', 'POST'])
def yy_data():
    if request.method == 'GET':
        session['current_file'] = 'yy.txt'  # Explicitly set default file for this route
    return process_request(['yy'], 'yy.txt')

@app.route('/phyy38', methods=['GET', 'POST'])
def phyy_data():
    if request.method == 'GET':
        session['current_file'] = 'phyy.txt'  # Explicitly set default file for this route
    return process_request(['phyy'], 'phyy.txt')

@app.route('/allhen51', methods=['GET', 'POST'])
def allhen_data():
    if request.method == 'GET':
        session['current_file'] = 'allhen.txt'  # Explicitly set default file for this route
    return process_request(['hen', 'phhen'], 'allhen.txt')

@app.route('/allkun73', methods=['GET', 'POST'])
def allkun_data():
    if request.method == 'GET':
        session['current_file'] = 'allkun.txt'  # Explicitly set default file for this route
    return process_request(['kun', 'phkun'], 'allkun.txt')

@app.route('/allten26', methods=['GET', 'POST'])
def allten_data():
    if request.method == 'GET':
        session['current_file'] = 'allten.txt'  # Explicitly set default file for this route
    return process_request(['ten', 'phten'], 'allten.txt')

@app.route('/allxj89', methods=['GET', 'POST'])
def allxj_data():
    if request.method == 'GET':
        session['current_file'] = 'allxj.txt'  # Explicitly set default file for this route
    return process_request(['xj', 'phxj'], 'allxj.txt')

@app.route('/allyy42', methods=['GET', 'POST'])
def allyy_data():
    if request.method == 'GET':
        session['current_file'] = 'allyy.txt'  # Explicitly set default file for this route
    return process_request(['yy', 'phyy'], 'allyy.txt')

@app.route('/wayv65', methods=['GET', 'POST'])
def wayv_data():
    if request.method == 'GET':
        session['current_file'] = 'wayv.txt'  # Explicitly set default file for this route
    return process_request(['wayv'], 'wayv.txt')

@app.route('/way37', methods=['GET', 'POST'])
def way_data():
    if request.method == 'GET':
        session['current_file'] = 'yy.txt'  # Explicitly set default file for this route
    return process_request(['wayv','kun','phkun','yy','phyy','ten','phten','hen','phhen','xj','phxj'], 'yy.txt')


@app.route('/phhyeonbin11', methods=['GET', 'POST'])
def phhyeonbin_data():
    if request.method == 'GET':
        session['current_file'] = 'phhyeonbin.txt'  # Explicitly set default file for this route
    return process_request(['phhyeonbin'], 'phhyeonbin.txt')





if __name__ == '__main__':
    app.run(host='0.0.0.0', port=9988, debug=True)
