from flask import Flask, render_template_string, request, session
import os
import numpy as np
import logging
from datetime import datetime
from collections import deque

# 定义库存处理类
class InventoryProcessor:
    def __init__(self, threshold=None, highlight_num=40, is_ph_file=False, is_ma=False):
        # 统一阈值设置
        self.threshold = threshold if threshold is not None else (500 if is_ma else 270 if is_ph_file else 190)
        self.highlight_num = highlight_num
        self.is_ph_file = is_ph_file
        self.is_ma = is_ma

    @staticmethod
    def parse_time(time_str):
        time_str = time_str.strip().rstrip(':')
        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"]:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        logging.error(f"无法解析日期时间字符串: {time_str}")
        return datetime.min

    def process_queue(self, raw_value, pending, valid, time_window, current_ts):
        processed_value = int(round(raw_value))
        if processed_value < 0:
            qty = abs(processed_value)
            sale_record = {
                'timestamp': current_ts,
                'original': qty,
                'remaining': qty,
                'valid': qty <= self.threshold
            }
            pending.append(sale_record)
            if sale_record['valid']:
                valid.append(qty)
            return qty
        else:
            refund = abs(processed_value)
            for i in range(len(pending) - 1, -1, -1):
                record = pending[i]
                time_diff = (self.parse_time(current_ts) - self.parse_time(record['timestamp'])).total_seconds()
                if time_diff <= time_window and record['valid'] and record['original'] == refund:
                    try:
                        valid.remove(refund)
                    except ValueError:
                        pass
                    del pending[i]
                    return refund
            return 0

    def process_data(self, data):
        # 如果数据为空，直接返回空结果
        if not data:
            return [], [], 0, 0

        processed_data = []
        app_pending = deque(maxlen=100)  # 限制队列大小
        shop_pending = deque(maxlen=100)  # 限制队列大小
        app_valid = []
        shop_valid = []
        total_sales = 0
        app_sales = 0  # 添加 app_sales 计算

        # 预先分配足够的容量
        processed_data = [None] * len(data)
        idx = 0

        for line in data:
            if not line.strip():
                continue

            parts = line.split(',')
            if len(parts) < 5:
                continue

            try:
                ts = parts[0].strip()
                raw_sold = float(parts[2])
                raw_stock = float(parts[4])

                # 筛选并计算总销量
                if abs(raw_sold) < self.threshold:
                    total_sales += raw_sold
                if abs(raw_stock) < self.threshold:
                    total_sales += raw_stock

                # 计算 app 端的销量
                if abs(raw_sold) <= self.threshold:
                    app_sales += raw_sold


                self.process_queue(-raw_sold, app_pending, app_valid, 86400, ts)
                self.process_queue(-raw_stock, shop_pending, shop_valid, 86400, ts)

                # 直接在指定位置存储，避免append的开销
                filtered = abs(raw_sold) > self.threshold or abs(raw_stock) > self.threshold
                processed_data[idx] = (line, filtered, raw_sold, raw_stock)
                idx += 1

            except Exception as e:
                logging.error(f"数据处理失败: {line} - {str(e)}")
                continue

        # 删除未使用的元素
        processed_data = processed_data[:idx]

        # 保持时间降序排列
        processed_data.reverse()

        # 高效合并并排序
        combined = sorted(app_valid + shop_valid, reverse=True)
        if self.highlight_num < len(combined):
            combined = combined[:self.highlight_num]

        return processed_data, combined, total_sales, app_sales

    @staticmethod
    def calculate_stats(values):
        if not values:
            return {'min': 0, 'max': 0, 'median': 0, 'mean': 0}
        arr = np.array(values)
        return {
            'min': int(np.min(arr).item()),
            'max': int(np.max(arr).item()),
            'median': float(np.median(arr).item()),
            'mean': round(float(np.mean(arr)), 2)
        }

# 配置日志记录
logging.basicConfig(level=logging.INFO)
app = Flask(__name__)
app.secret_key = 'your_secure_secret_key_here'  # 请替换为安全的密钥
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 86400  # 静态资源缓存1天

@app.context_processor
def utility_processor():
    return dict(abs=abs)


def get_available_files():
    file_paths = {}
    current_dir = os.getcwd()  # 获取当前工作目录

    # 遍历当前目录下的所有文件
    for file in os.listdir(current_dir):
        if file.endswith('.txt'):
            # 使用文件名(不带扩展名)作为键
            key = os.path.splitext(file)[0]
            file_paths[key] = file

    return file_paths

# 获取可用的文件列表
def get_available_buttons():
    return list(get_available_files().keys())

# ... 文件头部保持不变 ...

# 修改主题色为浅紫色
template = '''
<html>
<head>
    <title>水獭签售数据平台</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
        :root {
            --primary-color: #9575cd;
            --primary-light: #d1c4e9;
            --primary-dark: #65499c;
            --text-color: #333;
            --card-bg: #ffffff;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            font-size: 13px;
        }

        body {
            background-color: #faf7ff;
            color: var(--text-color);
            line-height: 1.3;
            padding: 8px;
        }

        .container {
            max-width: 100%;
        }

        .header {
            text-align: center;
            padding: 8px 0;
            margin-bottom: 10px;
        }

        .header h1 {
            color: var(--primary-dark);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 3px;
        }

        .compact-card {
            background: var(--card-bg);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .card-title {
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--primary-light);
            display: flex;
            align-items: center;
        }

        .card-title .icon {
            background: var(--primary-light);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: var(--primary-dark);
            font-size: 0.8rem;
        }

        .card-title h2 {
            color: var(--primary-dark);
            font-size: 1rem;
            font-weight: 600;
        }

        /* 按钮组 - 自动换行 */
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 8px 0;
            padding-bottom: 3px;
        }

        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            flex-shrink: 0;
            white-space: nowrap;
        }

        /* 紧凑表单 - 单行 */
        .form-row {
            display: flex;
            flex-wrap: nowrap;
            gap: 6px;
            margin: 8px 0;
            overflow-x: auto;
        }

        .form-row input {
            min-width: 80px;
            padding: 6px 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        /* 数据模块 - 合并布局 */
        .data-module {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .data-section {
            background: linear-gradient(135deg, #f5f2ff, #edebff);
            border-radius: 4px;
            padding: 10px;
            border-left: 2px solid var(--primary-color);
        }

        .data-section h3 {
            color: var(--primary-dark);
            font-size: 0.85rem;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .data-values {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 8px;
        }

        .data-value {
            background: var(--primary-light);
            color: var(--primary-dark);
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            min-width: 40px;
            text-align: center;
        }

        .data-value.large {
            font-size: 1.1rem;
            font-weight: 700;
            min-width: 60px;
        }

        .data-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
            margin-top: 8px;
        }

        .stat-item {
            background: #f0edff;
            padding: 5px;
            border-radius: 3px;
            font-size: 0.75rem;
        }

        /* 高光数据 - 紧凑 */
        .highlight-block {
            background: #f8f7ff;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #e8e6ff;
        }

        .highlight-block h4 {
            color: var(--primary-dark);
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .highlight-row {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            max-height: 80px;
            overflow-y: auto;
        }

        .highlight-item {
            background: var(--primary-light);
            color: var(--primary-dark);
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 0.75rem;
            min-width: 30px;
            text-align: center;
        }

        /* 库存变化表格 */
        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
            table-layout: fixed;
        }

        .inventory-table th {
            background: var(--primary-color);
            color: white;
            padding: 6px;
            text-align: left;
            font-size: 0.75rem;
            position: sticky;
            top: 0;
        }

        .inventory-table td {
            padding: 5px 6px;
            border-bottom: 1px solid #eee;
            font-size: 0.75rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .inventory-table tr:nth-child(even) {
            background-color: #fcfbff;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 15px;
            border-radius: 8px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            position: relative;
        }
        
        .close {
            color: #aaa;
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .modal-content h3 {
            color: var(--primary-dark);
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .modal-content p {
            margin: 8px 0;
            line-height: 1.5;
            font-size: 0.85rem;
        }
        
        /* 库存变化容器 */
        .inventory-container {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 8px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        /* 工具按钮组 - 单行 */
        .toolbar {
            display: flex;
            flex-wrap: nowrap;
            gap: 6px;
            margin: 8px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦦 水獭签售</h1>
        </div>
        
        <input type="hidden" id="currentFileName" value="{{ file_name }}">
        
        <div class="compact-card">
            <div class="card-title">
                <div class="icon">📊</div>
                <h2>文件选择</h2>
            </div>
            
            {% if show_buttons %}
            <form method="post">
                <div class="button-group">
                    {% for name in buttons %}
                    <button type="submit" name="{{ name }}">{{ name|upper }}</button>
                    {% endfor %}
                </div>
            </form>
            {% endif %}
            
            <div class="form-row">
                <input type="number" name="threshold" placeholder="阈值" step="1" min="0">
                <input type="number" name="highlight_num" placeholder="高光数量" min="1">
                <input type="number" name="cut_value" placeholder="除数" step="0.1" min="0">
                <button type="submit">更新</button>
            </div>
            
            <div class="toolbar">
                <div style="display:flex;align-items:center;">
                    <input type="checkbox" id="autoRefresh" style="margin-right:4px;">
                    <label for="autoRefresh" style="font-size:0.8rem;">自动刷新</label>
                </div>
                <button type="button" id="exportExcel" style="background:var(--primary-dark);">导出</button>
                <button type="button" id="helpButton" style="background:#5e35b1;">说明</button>
            </div>
        </div>
        
        <div id="helpModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>平台使用指南</h3>
                <p>🔄 <strong>自动刷新</strong>：实时更新数据</p>
                <p>⚙️ <strong>阈值</strong>：区分正常销售和注水数据</p>
                <p>🧮 <strong>除数</strong>：计算安全位置 (总销量÷除数)</p>
                <p>📤 <strong>导出</strong>：保存当前数据到Excel</p>
            </div>
        </div>
        
        <div class="compact-card">
            <div class="card-title">
                <div class="icon">📈</div>
                <h2>{{ file_name|upper }} 数据</h2>
            </div>
            
            <!-- 合并的数据模块 -->
            <div class="data-module">
                <div class="data-section">
                    <h3>核心指标</h3>
                    <div class="data-values">
                        <div class="data-value">总销量</div>
                        <div class="data-value large">{{ total if total is not none else '0' }}</div>
                        <div class="data-value">glb销量</div>                      
                        <div class="data-value large">{{ kms_total if kms_total is not none else '0' }}</div>
                    </div>
                    
                    {% if cut is not none %}
                    <div class="data-values" style="margin-top:10px;">
                        <div class="data-value large">{{ cut }}</div>
                        <div class="data-value">安全值</div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="data-section">
                    <h3>数据分析</h3>
                    {% if stats is not none %}
                    <div class="data-stats">
                        <div class="stat-item">最小: {{ stats.min }}</div>
                        <div class="stat-item">最大: {{ stats.max }}</div>
                        <div class="stat-item">中位: {{ stats.median }}</div>
                        <div class="stat-item">平均: {{ stats.mean }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 高光数据部分 -->
            <div class="highlight-block">
                <h4>高光数据 (TOP {{ highlight_num }})</h4>
                <div class="highlight-row">
                    {% for value in highlighted_values if highlighted_values is not none %}
                    <span class="highlight-item">{{ value }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="compact-card">
            <div class="card-title">
                <div class="icon">📋</div>
                <h2>销量变化</h2>
            </div>
            
            <div class="inventory-container">
                <table class="inventory-table">
                    <thead>
                        <tr>
                            <th style="width:40%;">时间</th>
                            <th style="width:30%;">glb变化</th>
                            <th style="width:30%;">台湾变化</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for line, filtered, real_sold, real_stock in data %}
                        {% set parts = line.split(',') %}
                        <tr>
                            <td>{{ parts[0] }}</td>
                            <td>{{ parts[2] }}</td>
                            <td>{{ parts[4] }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 弹窗功能
            var modal = document.getElementById('helpModal');
            var btn = document.getElementById('helpButton');
            var span = document.getElementsByClassName('close')[0];
            
            btn.onclick = function() {
                modal.style.display = "block";
            }
            
            span.onclick = function() {
                modal.style.display = "none";
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // 自动刷新功能
            var autoRefreshCheckbox = document.getElementById('autoRefresh');
            var refreshIntervalId = null;
            
            if (autoRefreshCheckbox) {
                var currentRoute = window.location.pathname;
                var storageKey = 'autoRefreshEnabled_' + currentRoute;
                
                var savedState = localStorage.getItem(storageKey);
                if (savedState === 'true') {
                    autoRefreshCheckbox.checked = true;
                    startAutoRefresh();
                }
                
                autoRefreshCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                        localStorage.setItem(storageKey, 'true');
                    } else {
                        stopAutoRefresh();
                        localStorage.setItem(storageKey, 'false');
                    }
                });
                
                window.addEventListener('beforeunload', function() {
                    if (refreshIntervalId !== null) {
                        clearInterval(refreshIntervalId);
                    }
                    localStorage.setItem(storageKey, autoRefreshCheckbox.checked ? 'true' : 'false');
                });
            }
            
            function startAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                }
                
                var currentFileName = document.getElementById('currentFileName').value;
                var buttons = document.querySelectorAll('form button[type="submit"]');
                var currentButton = null;
                
                for (var i = 0; i < buttons.length; i++) {
                    if (buttons[i].name === currentFileName) {
                        currentButton = buttons[i];
                        break;
                    }
                }
                
                if (currentButton) {
                    refreshIntervalId = setInterval(function() {
                        currentButton.click();
                    }, 3000);
                }
            }
            
            function stopAutoRefresh() {
                if (refreshIntervalId !== null) {
                    clearInterval(refreshIntervalId);
                    refreshIntervalId = null;
                }
            }
            
            // 导出Excel功能
            var exportBtn = document.getElementById('exportExcel');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    exportToExcel();
                });
            }
            
            function exportToExcel() {
                try {
                    const wb = XLSX.utils.book_new();
                    const currentFileName = document.getElementById('currentFileName').value || '水獭签售数据';
                    
                    const tableHeaders = Array.from(document.querySelectorAll('.inventory-table th'))
                        .map(th => th.textContent.trim());
                    
                    const tableRows = [];
                    document.querySelectorAll('.inventory-table tbody tr').forEach(tr => {
                        const row = Array.from(tr.querySelectorAll('td'))
                            .map(td => td.textContent.trim());
                        tableRows.push(row);
                    });
                    
                    const ws = XLSX.utils.aoa_to_sheet([tableHeaders, ...tableRows]);
                    XLSX.utils.book_append_sheet(wb, ws, currentFileName + "数据");
                    
                    const fileName = currentFileName + '_数据_' + new Date().toISOString().split('T')[0] + '.xlsx';
                    XLSX.writeFile(wb, fileName);
                    
                    alert('导出成功!');
                } catch (error) {
                    console.error('导出失败:', error);
                    alert('导出错误: ' + error.message);
                }
            }
        });
    </script>
</body>
</html>
'''


def parse_time(time_str):
    return InventoryProcessor.parse_time(time_str)

def read_file(file_path):
    if not os.path.isfile(file_path):
        # 如果文件不存在，尝试在当前目录查找
        current_dir = os.getcwd()
        base_filename = os.path.basename(file_path)
        alternative_path = os.path.join(current_dir, base_filename)

        if os.path.isfile(alternative_path):
            file_path = alternative_path
        else:
            logging.warning(f"文件不存在: {file_path}")
            return []
    try:
        file_mtime = os.path.getmtime(file_path)
        cache_key = (file_path, file_mtime)

        # 使用缓存数据，如果存在
        if cache_key in read_file.cache:
            return read_file.cache[cache_key]

        with open(file_path, 'r') as file:
            lines = [line.strip() for line in file if line.strip()]

            # 优化排序 - 只在必要时进行排序
            if lines and ',' in lines[0]:  # 确保文件格式正确
                lines.sort(key=lambda x: parse_time(x.split(',')[0]))

            # 维护缓存大小，防止内存泄漏
            if len(read_file.cache) > 20:  # 限制缓存条目数量
                # 删除最旧的缓存项
                oldest_key = next(iter(read_file.cache))
                del read_file.cache[oldest_key]

            read_file.cache[cache_key] = lines
            return lines
    except Exception as e:
        logging.error(f"文件读取失败: {e}")
        return []

# 初始化缓存字典
read_file.cache = {}

# 添加结果缓存字典
process_request_cache = {}

def process_request(buttons, default_file, highlight_num_override=None, start=0, end=None):
    # 检测是否为AJAX请求
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

    # 构建缓存键
    cache_key = (
        tuple(buttons),
        default_file,
        highlight_num_override,
        start,
        end,
        request.form.get('threshold'),
        request.form.get('highlight_num'),
        request.form.get('cut_value'),
        is_ajax  # 添加AJAX状态到缓存键
    )

    # 检查是否有表单提交操作，这会导致状态变化
    form_submitted = False
    for key in buttons:
        if key in request.form:
            form_submitted = True
            break

    # 检查是否有表单设置提交
    settings_submitted = 'threshold' in request.form or 'highlight_num' in request.form or 'cut_value' in request.form

    # 如果没有表单提交且缓存中有结果，直接返回缓存结果
    if not form_submitted and not settings_submitted and cache_key in process_request_cache:
        return process_request_cache[cache_key]

    selected_button = None
    for key in buttons:
        if key in request.form:
            selected_button = key
            break

    # 获取当前可用的文件
    file_paths = get_available_files()

    # 只有在点击按钮时更改当前文件
    if selected_button and selected_button in file_paths:
        session['current_file'] = file_paths[selected_button]
    # 如果是首次访问该路由，设置默认文件
    elif 'current_file' not in session:
        session['current_file'] = default_file
    # 否则保持当前文件不变

    file_to_show = session['current_file']
    data = read_file(file_to_show)
    is_ph = 'ph' in file_to_show.lower()
    is_ma = any(kw in file_to_show.lower() for kw in ['ma', 'mark'])
    is_moon ='moon' in file_to_show.lower()
    # 获取当前显示的文件名
    file_name = next((k for k, v in file_paths.items() if v == file_to_show), '')
    # 如果在URL中没有找到匹配的键，使用文件路径的基本名称
    if not file_name:
        file_name = os.path.basename(file_to_show).split('.')[0]

    try:
        threshold = int(request.form.get('threshold')) if request.form.get('threshold') else None
    except:
        threshold = None

    # 特定文件的高光数量设置
    if file_name in ['kickflip', 'ahof']:
        default_highlight_num = 150  # jack的高光个数已设置为150个
    elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion']:
        default_highlight_num = 40
    elif file_name in ['smn','allen','hyeongjun','jungmo','minhee','seongmin','serim','taeyoung','wonjin','woobin']:  # photo高光设置10个
        default_highlight_num = 10
    elif file_name in ['yy','kun','xj','ten','hen','renjun','jaemin','mark','haechan','chenle','closeyoureye','coffee','soobin', 'yeonjun', 'beomgyu', 'taehyun', 'huenigkai','seulgi','irene','alldayproject']:
        default_highlight_num = 30
    elif file_name == 'serim':  # gidle高光设置为250个
        default_highlight_num = 10
    elif file_name in ['hyunsik','stayc']: # shjack的高光个数设置为150个
        default_highlight_num = 6
    elif file_name  in ['yenasz', 'yenagz']:  # ive的高光个数设置为200个
        default_highlight_num = 100
    elif file_name in ['合影应募','soyeon', 'yuqi', 'shuhua', 'miyeon', 'minnie']:
        default_highlight_num = 10
    elif file_name in ['jisung','jeno','mark','renjun','anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin']:
        default_highlight_num = 45  # 修改为30个
    elif 'ph' in file_name:  # 含ph的高光个数设置为10个
        default_highlight_num = 10
    else:
        default_highlight_num = 10

    if highlight_num_override is not None:
        highlight_num = highlight_num_override
    elif request.form.get('highlight_num'):
        try:
            highlight_num = int(request.form['highlight_num'])
        except ValueError:
            highlight_num = default_highlight_num
    else:
        highlight_num = default_highlight_num

    if file_name =='':
        highlight_num = 1016
    elif file_name == '':
        highlight_num = 593

    # 调整最大高光数量限制
    if file_name == 'doyoung':
        max_highlight = 200
    elif file_name in []:
        max_highlight = 20
    else:
        max_highlight = 260

    highlight_num = min(highlight_num, max_highlight)

    processor = InventoryProcessor(
        threshold=threshold,
        highlight_num=highlight_num,
        is_ph_file=is_ph,
        is_ma=is_ma
    )

    processed, highlighted, total, kms_total = processor.process_data(data)

    # 处理特定文件的总销量调整
    if file_name == '签名应募':
        total += 419  # photo总销量加上1880
    elif file_name == '合影签售':
        total += 1208  # sign总销量加上4096
    elif file_name == 'coffee':
        total += 413   # jack总销量加上269
    elif file_name == 'kissoflife':
        total += 187   # nexz总销量加上319
    elif file_name == 'le':
        total += 483
    highlighted = highlighted if highlighted else []
    stats = InventoryProcessor.calculate_stats(highlighted)

    # Initialize all stats variables as None
    stats_176_275 = None
    stats_1_25 = None
    stats_26_40 = None
    stats_41_50 = None  # 新增41-50区间统计
    stats_1_50 = None
    stats_51_250 = None
    stats_51_200 = None
    stats_51_190 = None
    stats_51_150 = None
    stats_61_100 = None
    stats_101_140 = None
    stats_101_150 = None
    stats_61_140 = None
    stats_141_240 = None
    stats_26_50 = None
    stats_1_5 = None
    stats_1_15 = None
    stats_16_30 = None
    stats_160_220 = None
    stats_51_100 = None
    stats_1_20 = None
    stats_21_30 = None
    stats_151_250 = None
    stats_51_125 = None
    stats_126_200 = None
    stats_26_35 = None
    stats_1_6 = None  # 新增1-6区间统计
    stats_7_12 = None  # 新增7-12区间统计
    # 新增区间统计
    stats_1_125 = None  # wayv 1-125
    stats_126_225 = None  # wayv 126-225
    stats_6_10 = None  # soyeon 6-10
    stats_1_60 = None  # superjunior 1-60
    stats_61_260 = None  # superjunior 61-260
    stats_1_10 = None  # cravity 1-10
    stats_15_20 = None  # cravity 15-20

    # Calculate stats based on file_name
    if file_name == 'all':
        values_176_275 = highlighted[175:275]
        stats_176_275 = InventoryProcessor.calculate_stats(values_176_275)
    elif file_name in ['yy','kun','xj','ten','hen','renjun','jaemin','mark','haechan','chenle','coffee','soobin', 'yeonjun', 'beomgyu', 'taehyun', 'huenigkai','seulgi','irene','alldayproject','closeyoureye']:
        values_1_25 = highlighted[:15]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_40 = highlighted[15:30]
        stats_26_40 = InventoryProcessor.calculate_stats(values_26_40)
    elif file_name == 'ive':
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[50:200]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
        values_51_125 = highlighted[50:125]
        stats_51_125 = InventoryProcessor.calculate_stats(values_51_125)
        values_126_200 = highlighted[125:200]
        stats_126_200 = InventoryProcessor.calculate_stats(values_126_200)
    elif file_name in ['sevenus', '']: # 添加riize的分段统计
        values_1_50 = highlighted[:10]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_190 = highlighted[10:20]
        stats_51_190 = InventoryProcessor.calculate_stats(values_51_190)
    elif file_name in ['kickflip', 'ahof']: # 新增sign分段统计
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[50:150]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
    elif file_name == '1':  # 修改gidle分段统计为1-50, 51-250
        values_1_50 = highlighted[:50]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_250 = highlighted[50:250]
        stats_51_250 = InventoryProcessor.calculate_stats(values_51_250)
        values_51_150 = highlighted[50:150]
        stats_51_150 = InventoryProcessor.calculate_stats(values_51_150)
        values_151_250 = highlighted[150:250]
        stats_151_250 = InventoryProcessor.calculate_stats(values_151_250)
    elif file_name == 'face':  # 新增shjack分段统计
        values_1_50 = highlighted[:35]
        stats_1_50 = InventoryProcessor.calculate_stats(values_1_50)
        values_51_200 = highlighted[35:70]
        stats_51_200 = InventoryProcessor.calculate_stats(values_51_200)
    elif file_name in ['single']:
        values_1_25 = highlighted[:40]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_50 = highlighted[40:80]
        stats_26_50 = InventoryProcessor.calculate_stats(values_26_50)
    elif file_name in ['sakuya', 'riku', 'yushi', 'jaehee', 'ryo', 'sion']:
        # 修改为1-25, 26-40, 41-50区间统计
        values_1_25 = highlighted[:min(15, len(highlighted))]
        stats_1_25 = InventoryProcessor.calculate_stats(values_1_25)
        values_26_40 = highlighted[15:min(30, len(highlighted))]
        stats_26_40 = InventoryProcessor.calculate_stats(values_26_40)
        values_41_50 = highlighted[40:min(50, len(highlighted))]
        stats_41_50 = InventoryProcessor.calculate_stats(values_41_50)
    elif 'ph' in file_name:
        # 修改含ph文件为1-6, 7-12区间统计
        values_1_6 = highlighted[:min(6, len(highlighted))]
        stats_1_6 = InventoryProcessor.calculate_stats(values_1_6)
        values_7_12 = highlighted[6:min(12, len(highlighted))]
        stats_7_12 = InventoryProcessor.calculate_stats(values_7_12)
    elif file_name in ['jeno','jisung','mark','renjun','anton', 'eunseok', 'shotaro', 'sohee', 'sungchan', 'wonbin']:
        # 新增这些文件的1-15和16-30区间统计
        values_1_15 = highlighted[:min(25, len(highlighted))]
        stats_1_15 = InventoryProcessor.calculate_stats(values_1_15)
        values_16_30 = highlighted[25:min(45, len(highlighted))]
        stats_16_30 = InventoryProcessor.calculate_stats(values_16_30)
    elif file_name == 'kissoflife':
        values_1_125 = highlighted[:50]
        stats_1_125 = InventoryProcessor.calculate_stats(values_1_125)
        values_126_225 = highlighted[50:100]
        stats_126_225 = InventoryProcessor.calculate_stats(values_126_225)
    elif file_name in ['yenasz', 'yenagz']:
        values_1_5 = highlighted[:30]
        stats_1_5 = InventoryProcessor.calculate_stats(values_1_5)
        values_6_10 = highlighted[30:100]
        stats_6_10 = InventoryProcessor.calculate_stats(values_6_10)
    elif file_name in ['stayc','签名应募']:
        values_1_60 = highlighted[:50]
        stats_1_60 = InventoryProcessor.calculate_stats(values_1_60)
        values_61_260 = highlighted[50:200]
        stats_61_260 = InventoryProcessor.calculate_stats(values_61_260)
    elif file_name in ['合影应募','smn','allen', 'hyeongjun', 'jungmo', 'minhee', 'seongmin', 'serim', 'taeyoung', 'wonjin', 'woobin']:
        values_1_10 = highlighted[:5]
        stats_1_10 = InventoryProcessor.calculate_stats(values_1_10)
        values_15_20 = highlighted[5:10]
        stats_15_20 = InventoryProcessor.calculate_stats(values_15_20)
    cut = None
    if 'cut_value' in request.form:
        try:
            divisor = float(request.form['cut_value'])
            cut = total / divisor if divisor != 0 else "除零错误"
        except:
            cut = "无效输入"

    # 准备渲染结果
    result = render_template_string(template,
                                    show_buttons=True,
                                    buttons=buttons,
                                    data=processed,
                                    highlighted_values=highlighted,
                                    stats=stats,
                                    total=int(total),
                                    kms_total=int(kms_total),
                                    highlight_num=highlight_num,
                                    cut=cut,
                                    file_name=file_name,
                                    stats_176_275=stats_176_275,
                                    stats_1_25=stats_1_25,
                                    stats_26_40=stats_26_40,
                                    stats_41_50=stats_41_50,  # 添加新的统计变量
                                    stats_1_50=stats_1_50,
                                    stats_51_250=stats_51_250,
                                    stats_51_200=stats_51_200,
                                    stats_51_190=stats_51_190,
                                    stats_51_150=stats_51_150,
                                    stats_61_100=stats_61_100,
                                    stats_101_140=stats_101_140,
                                    stats_61_140=stats_61_140,
                                    stats_141_240=stats_141_240,
                                    stats_26_50=stats_26_50,
                                    stats_51_100=stats_51_100,
                                    stats_160_220=stats_160_220,
                                    stats_1_5=stats_1_5,
                                    stats_1_15=stats_1_15,
                                    stats_16_30=stats_16_30,
                                    stats_1_20=stats_1_20,
                                    stats_21_30=stats_21_30,
                                    stats_151_250=stats_151_250,
                                    stats_51_125=stats_51_125,
                                    stats_126_200=stats_126_200,
                                    stats_26_35=stats_26_35,
                                    stats_1_6=stats_1_6,  # 添加新的统计变量
                                    stats_7_12=stats_7_12,  # 添加新的统计变量
                                    stats_1_125=stats_1_125,  # 添加新的统计变量
                                    stats_126_225=stats_126_225,  # 添加新的统计变量
                                    stats_6_10=stats_6_10,  # 添加新的统计变量
                                    stats_1_60=stats_1_60,  # 添加新的统计变量
                                    stats_61_260=stats_61_260,  # 添加新的统计变量
                                    stats_1_10=stats_1_10,
                                    stats_15_20=stats_15_20)  # 添加新的统计变量

    # 缓存结果，仅在表单提交时不缓存
    if not form_submitted:
        # 维护缓存大小，防止内存溢出
        if len(process_request_cache) > 30:  # 限制缓存条目数量
            # 删除一个随机键以避免同时删除所有缓存
            random_key = next(iter(process_request_cache))
            del process_request_cache[random_key]
        process_request_cache[cache_key] = result

    return result



@app.route('/tb02', methods=['GET', 'POST'])
def tb02_data():
    buttons = get_available_buttons()
    session['current_file'] = 'sion.txt'  # Explicitly set default file for this route
    return process_request(buttons, 'sion.txt')

@app.route('/jisung1', methods=['GET', 'POST'])
def jisung_data():
    session['current_file'] = 'jisung.txt'  # Explicitly set default file for this route
    return process_request(['jisung'], 'jisung.txt')

@app.route('/sevenus2', methods=['GET', 'POST'])
def sevenus_data():
    session['current_file'] = 'sevenus.txt'  # Explicitly set default file for this route
    return process_request(['sevenus'], 'sevenus.txt')

@app.route('/jeno3', methods=['GET', 'POST'])
def jeno_data():
    session['current_file'] = 'jeno.txt'  # Explicitly set default file for this route
    return process_request(['jeno'], 'jeno.txt')

@app.route('/mark4', methods=['GET', 'POST'])
def mark_data():
    session['current_file'] = 'mark.txt'  # Explicitly set default file for this route
    return process_request(['mark'], 'mark.txt')

@app.route('/renjun5', methods=['GET', 'POST'])
def renjun_data():
    session['current_file'] = 'renjun.txt'  # Explicitly set default file for this route
    return process_request(['renjun'], 'renjun.txt')

@app.route('/hyunsik6', methods=['GET', 'POST'])
def hyunsik_data():
    session['current_file'] = 'hyunsik.txt'  # Explicitly set default file for this route
    return process_request(['hyunsik'], 'hyunsik.txt')

@app.route('/serim7', methods=['GET', 'POST'])
def serim_data():
    session['current_file'] = 'serim.txt'  # Explicitly set default file for this route
    return process_request(['serim'], 'serim.txt')


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=4433, debug=True)