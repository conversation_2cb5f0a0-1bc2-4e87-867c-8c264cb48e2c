import requests
import json
from typing import Dict, List, Optional

class EcommerceScraper:
    def __init__(self):
        # 设置请求头，模拟浏览器访问
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://m.ecom.mgtv.com/mapp/order-list?status=2',
            'Origin': 'https://m.ecom.mgtv.com',
            'Connection': 'keep-alive',
        }
        # 商品ID与名称的映射关系
        self.product_id_map = {
            251976: "【视频通话签售会】R.E.D《釉》实体专辑",
            251975: "【深圳线下签售会】R.E.D《釉》实体专辑"
        }

    def fetch_product_data(self, page_num: int = 1, page_size: int = 20) -> Dict:
        """
        从API获取商品列表数据

        Args:
            page_num: 页码
            page_size: 每页数量

        Returns:
            包含商品信息的字典
        """
        url = "https://mgesq.api.mgtv.com/goods/guess_you_like"
        params = {
            "device": "b137c1e5-f993-477f-93f8-36dc01071015",
            "deviceid": "b137c1e5-f993-477f-93f8-36dc01071015",
            "osVersion": "",
            "did": "b137c1e5-f993-477f-93f8-36dc01071015",
            "mod": "",
            "mf": "",
            "net": "",
            "callback": "",
            "_support": "10100001",
            "osType": "h5",
            "h5osSys": "android",
            "src": "",
            "origin": "mweb",
            "rch": "",
            "source_channel": "",
            "smdid": "WHJMrwNw1k%2FGuSqpE%2Br55lYW%2Bg6JhqZLirbf6yxTSy0RV%2BrQxTF8fVkNdwQ46TcoE43kU6NWPVmXbs6NuoqVg0f6u9yqMtGjzdCW1tldyDzmQI99%2BchXEimEuJLzrMPhU9lCUKKcsmkSqmJzoPeggwzYmmmXo8LlTkQE5YcNLqNriNYPfoOP%2FbqQx9iplQ2JlFRRuSg%2FX1FURTkAd7H%2FeWqlxtSe0EjCbjGQ3lUT8aJUyf0LwOd0Sh91zoL2tfhpW1487582755342",
            "uastr": "Mozilla%2F5.0+(Linux%3B+Android****%3B+Nexus+5+Build%2FMRA58N)+AppleWebKit%2F537.36+(KHTML,+like+Gecko)+Chrome%2F138.0.0.0+Mobile+Safari%2F537.36+Edg%2F138.0.0.0",
            "uuid": "353530b708ea58b813e7ddb6df02ccc2",
            "ticket": "B12161F8A81D36439F95B62F1EC451FE",
            "xmtail": "u-1-1",
            "pageNum": page_num,
            "pageSize": page_size,
            "from": "payed",
            "hideColumn": 1
        }

        try:
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()  # 检查请求是否成功
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求出错: {e}")
            return {"status": 500, "data": []}

    def extract_sales_data(self, product_id: int, product_data: Dict) -> Optional[int]:
        """
        从商品数据中提取销售数量

        Args:
            product_id: 商品ID
            product_data: 商品数据

        Returns:
            销售数量，如果未找到则返回None
        """
        for item in product_data.get("data", []):
            # 修复：使用正确的键名 "goods_id"（小写g）
            if item.get("goods_id") == product_id:
                return item.get("sales_num", 0)
        return None

    def scrape_sales_data(self) -> Dict:
        """
        爬取目标商品的销售数据

        Returns:
            包含目标商品销售数据的字典
        """
        result = {}
        page_num = 1

        while True:
            # 获取当前页数据
            data = self.fetch_product_data(page_num=page_num)

            # 检查响应状态
            if data.get("status") != 200 or not data.get("data"):
                break

            # 提取目标商品的销售数据
            for product_id, product_name in self.product_id_map.items():
                if product_id not in result:  # 只提取一次
                    sales_num = self.extract_sales_data(product_id, data)
                    if sales_num is not None:
                        result[product_id] = {
                            "name": product_name,
                            "sales_num": sales_num
                        }

            # 如果已找到所有目标商品，或数据不足一页，结束循环
            if len(result) == len(self.product_id_map) or len(data["data"]) < 20:
                break

            page_num += 1

        return result

if __name__ == "__main__":
    scraper = EcommerceScraper()
    sales_data = scraper.scrape_sales_data()

    print("成功获取以下商品的销售数据：")
    for product_id, info in sales_data.items():
        print(f"{info['name']}: 销量 {info['sales_num']}")

    # 保存数据到JSON文件
    with open("sales_data.json", "w", encoding="utf-8") as f:
        json.dump(sales_data, f, ensure_ascii=False, indent=2)
    print("数据已保存到 sales_data.json")