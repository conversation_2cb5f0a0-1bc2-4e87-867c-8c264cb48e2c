import requests
import json
import time

# 商品详情API模板
DETAIL_API = "https://mgesq.api.mgtv.com/goods/detail"

# 目标商品ID
target_goods = {
    251976: "【视频通话签售会】R.E.D《釉》实体专辑",
    251975: "【深圳线下签售会】R.E.D《釉》实体专辑"
}

# 构造基础参数
base_params = {
    "device": "b137c1e5-f993-477f-93f8-36dc01071015",
    "deviceid": "b137c1e5-f993-477f-93f8-36dc01071015",
    "osVersion": "",
    "did": "b137c1e5-f993-477f-93f8-36dc01071015",
    "mod": "",
    "mf": "",
    "net": "",
    "callback": "",
    "_support": "10100001",
    "osType": "h5",
    "h5osSys": "android",
    "src": "",
    "origin": "mweb",
    "rch": "",
    "source_channel": "",
    "smdid": "WHJMrwNw1k/GuSqpE+r55lYW+g6JhqZLirbf6yxTSy0RV+rQxTF8fVkNdwQ46TcoE43kU6NWPVmXbs6NuoqVg0f6u9yqMtGjzdCW1tldyDzmQI99+chXEimEuJLzrMPhU9lCUKKcsmkSqmJzoPeggwzYmmmXo8LlTkQE5YcNLqNriNYPfoOP/bqQx9iplQ2JlFRRuSg/X1FURTkAd7H/eWqlxtSe0EjCbjGQ3lUT8aJUyf0LwOd0Sh91zoL2tfhpW1487582755342",
    "uastr": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********",
    "uuid": "353530b708ea58b813e7ddb6df02ccc2",
    "ticket": "B12161F8A81D36439F95B62F1EC451FE",
    "xmtail": "u-1-1"
}

# 设置请求头
headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********",
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Referer": "https://m.ecom.mgtv.com/mapp/order-list?status=2",
    "Origin": "https://m.ecom.mgtv.com",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "Connection": "keep-alive",
    "X-Requested-With": "XMLHttpRequest"
}

def get_goods_detail(goods_id):
    """获取商品详情"""
    params = base_params.copy()
    params["goods_id"] = str(goods_id)

    try:
        print(f"\n正在获取商品 {goods_id} 的详情...")
        start_time = time.time()

        response = requests.get(
            DETAIL_API,
            params=params,
            headers=headers,
            timeout=15
        )

        elapsed_time = time.time() - start_time
        print(f"响应状态码: {response.status_code}, 耗时: {elapsed_time:.2f}秒")

        if response.status_code != 200:
            print(f"请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return None

        data = response.json()

        # 保存响应数据用于分析
        filename = f"goods_{goods_id}_response.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"响应数据已保存到: {filename}")

        # 检查API状态
        if data.get("code") != 200:
            print(f"API错误: {data.get('msg', '未知错误')}")
            return None

        # 返回商品详情
        return data.get("data", {})

    except Exception as e:
        print(f"获取商品详情出错: {str(e)}")
        return None

def extract_sales_info(goods_data):
    """从商品数据中提取销售信息"""
    # 尝试多种可能的字段名
    possible_fields = ["sales_num", "sale_num", "sale_count", "sales_count"]

    for field in possible_fields:
        if field in goods_data:
            return goods_data[field]

    # 如果找不到标准字段，尝试在SKU信息中查找
    if "skus" in goods_data and goods_data["skus"]:
        for sku in goods_data["skus"]:
            for field in possible_fields:
                if field in sku:
                    return sku[field]

    # 最后尝试在商品基础信息中查找
    goods_info = goods_data.get("goods_info", {})
    for field in possible_fields:
        if field in goods_info:
            return goods_info[field]

    return None

def main():
    results = {}

    for goods_id, title in target_goods.items():
        print(f"\n{'='*50}")
        print(f"处理商品: {title} (ID: {goods_id})")

        goods_data = get_goods_detail(goods_id)

        if not goods_data:
            print(f"无法获取商品 {title} 的详情")
            results[title] = "获取失败"
            continue

        # 提取销售数量
        sales_num = extract_sales_info(goods_data)

        if sales_num is not None:
            print(f"销售数量: {sales_num}")
            results[title] = sales_num
        else:
            print("未找到销售数量字段")
            print("商品数据中的关键字段:")
            print(json.dumps({k: v for k, v in goods_data.items() if not isinstance(v, list) and not isinstance(v, dict)}, ensure_ascii=False, indent=2)[:500] + "...")
            results[title] = "未找到"

    # 打印最终结果
    print("\n" + "="*50)
    print("抓取结果汇总:")
    for title, sales in results.items():
        print(f"{title} : sales_num = {sales}")

if __name__ == "__main__":
    main()