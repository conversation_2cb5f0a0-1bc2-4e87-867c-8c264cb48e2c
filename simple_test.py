#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简化测试，不依赖numpy
def test_data_parsing():
    """测试新格式数据解析"""
    
    # 模拟新格式的数据行
    test_lines = [
        "2025-08-12 04:01:06:,500743,0,411768,0,500481,0",
        "2025-08-12 04:02:06:,500743,-10,411768,-5,500481,-8",
        "2025-08-12 04:03:06:,500743,5,411768,3,500481,2",
        "2025-08-12 04:04:06:,500743,-200,411768,-150,500481,-180",
    ]
    
    print("测试新格式数据解析:")
    print("格式: 时间,旧版app库存,旧版app变化,微店库存,微店变化,新版app库存,新版app变化")
    print()
    
    total_sales = 0
    app_sales = 0
    threshold = 190
    
    for line in test_lines:
        parts = line.split(',')
        if len(parts) < 5:
            continue
            
        try:
            ts = parts[0].strip()
            raw_sold = float(parts[2])  # 旧版app变化
            raw_stock = float(parts[4])  # 微店变化
            raw_new_app_change = 0.0
            if len(parts) >= 7:
                raw_new_app_change = float(parts[6])  # 新版app变化
            
            print(f"时间: {ts}")
            print(f"  旧版app变化: {raw_sold}")
            print(f"  微店变化: {raw_stock}")
            print(f"  新版app变化: {raw_new_app_change}")
            
            # 计算总销量（所有负值的绝对值之和）
            if abs(raw_sold) < threshold:
                total_sales += -raw_sold
            if abs(raw_stock) < threshold:
                total_sales += -raw_stock
            if abs(raw_new_app_change) < threshold:
                total_sales += -raw_new_app_change
            
            # 计算app销量（旧版app + 新版app）
            if abs(raw_sold) <= threshold:
                if raw_sold < 0:
                    app_sales += abs(raw_sold)
                else:
                    app_sales -= raw_sold
            
            if abs(raw_new_app_change) <= threshold:
                if raw_new_app_change < 0:
                    app_sales += abs(raw_new_app_change)
                else:
                    app_sales -= raw_new_app_change
            
            # 判断状态
            filtered = abs(raw_sold) > threshold or abs(raw_stock) > threshold or abs(raw_new_app_change) > threshold
            
            # 显示每个渠道的状态
            if raw_sold != 0:
                status = "退回" if raw_sold > 0 else ("正常" if not filtered else "注水")
                print(f"  旧版app状态: {status}")
            
            if raw_stock != 0:
                status = "退回" if raw_stock > 0 else ("正常" if not filtered else "注水")
                print(f"  微店状态: {status}")
            
            if raw_new_app_change != 0:
                status = "退回" if raw_new_app_change > 0 else ("正常" if not filtered else "注水")
                print(f"  新版app状态: {status}")
            
            print()
            
        except Exception as e:
            print(f"解析失败: {line} - {e}")
    
    print(f"总销量: {total_sales}")
    print(f"APP销量（旧版+新版）: {app_sales}")
    print()
    print("测试完成！新格式解析正常。")

if __name__ == "__main__":
    test_data_parsing()
