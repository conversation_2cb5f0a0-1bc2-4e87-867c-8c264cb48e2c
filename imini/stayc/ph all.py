import requests
import time
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置参数
API_URL = "https://www.imini.tv/mallorder/api/detail.php"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Referer": "https://www.imini.tv/xingjunwenhua/wap_pro/"
}

# 定义不同商品组
PRODUCT_GROUPS = {
    "J": {
        "products": {
            "58113993": "【大陆地址】J",
            "58113998": "【非大陆地址】J",
        },
        "log_file": "phj.txt"
    },
    "SEEUN": {
        "products": {
            "58113991": "【大陆地址】SEEUN",
            "58113987": "【非大陆地址】SEEUN",
        },
        "log_file": "phseeun.txt"
    },
    "YOON": {
        "products": {
            "58113984": "【大陆地址】YOON",
            "58113985": "【非大陆地址】YOON",
        },
        "log_file": "phyoon.txt"
    },
    "SIEUN": {
        "products": {
            "58110675": "【大陆地址】SIEUN",
            "58110664": "【非大陆地址】SIEUN",
        },
        "log_file": "phsieun.txt"
    },
    "SUMIN": {
        "products": {
            "58110603": "【大陆地址】SUMIN",
            "58110657": "【非大陆地址】SUMIN",
        },
        "log_file": "phsumin.txt"
    },
    "ISA": {
        "products": {
            "58110467": "【大陆地址】ISA",
            "58110515": "【非大陆地址】ISA",
        },
        "log_file": "phisa.txt"
    }
}

INTERVAL = 0.7  # 监控间隔(秒)


class StockMonitor:
    def __init__(self, products, log_file):
        self.products = products
        self.log_file = log_file
        self.previous_stocks = {pro_id: None for pro_id in products}
        self.is_first_refresh = True

    def fetch_stock(self, pro_id):
        """获取商品库存"""
        try:
            params = {
                "pro_id": pro_id,
                "t": int(time.time() * 1000)
            }

            response = requests.get(
                API_URL,
                params=params,
                headers=HEADERS,
                verify=False,
                timeout=10
            )
            response.raise_for_status()

            data = response.json()
            if data.get("code") == "200":
                return int(data["data"].get("pro_stock", 0))
            return None

        except Exception as e:
            print(f"请求异常: {str(e)}")
            return None

    def format_output(self, all_stocks, all_changes):
        """生成标准化输出"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')
        output_parts = [current_time]
        for pro_id in self.products.keys():
            stock = all_stocks.get(pro_id, 0)
            change = all_changes.get(pro_id, 0)
            output_parts.extend([str(stock), str(change)])
        output = ','.join(output_parts)
        return output

    def log_data(self, all_stocks, all_changes):
        """记录所有商品数据到日志文件"""
        output = self.format_output(all_stocks, all_changes)
        print(output)
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(output + "\n")

    def monitor_once(self):
        """执行一次监控循环"""
        all_stocks = {}
        all_changes = {}
        any_change = False

        # 遍历所有需要监控的商品
        for pro_id in self.products.keys():
            stock = self.fetch_stock(pro_id)

            if stock is not None:
                previous = self.previous_stocks[pro_id]
                change = stock - previous if previous is not None else 0
                all_stocks[pro_id] = stock
                all_changes[pro_id] = change

                if change != 0:
                    any_change = True

                self.previous_stocks[pro_id] = stock

        # 判断是否为第一次刷新或者有库存变化
        if self.is_first_refresh or any_change:
            self.log_data(all_stocks, all_changes)
            if self.is_first_refresh:
                print(f"[{self.log_file}] 首次获取商品库存，记录所有商品初始库存信息。")
            self.is_first_refresh = False


if __name__ == "__main__":
    # 创建所有监控器实例
    monitors = []
    for group_name, group_data in PRODUCT_GROUPS.items():
        print(f"初始化商品组: {group_name}")
        monitor = StockMonitor(group_data["products"], group_data["log_file"])
        monitors.append(monitor)
        for pid, name in group_data["products"].items():
            print(f"  {pid}: {name}")
        print(f"  日志文件: {group_data['log_file']}")
        print()

    try:
        while True:
            # 依次执行每个监控器
            for monitor in monitors:
                monitor.monitor_once()

            # 等待指定间隔时间
            time.sleep(INTERVAL)

    except KeyboardInterrupt:
        print("\n监控已手动停止")