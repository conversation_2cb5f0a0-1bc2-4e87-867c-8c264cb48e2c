import requests
import time
import random
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 手动维护的User-Agent列表，模拟不同浏览器和设备
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 11; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
]

# 配置参数
API_URL = "https://www.imini.tv/mallorder/api/detail.php"

# 基础请求头，会在此基础上添加随机变化
BASE_HEADERS = {
    "Referer": "https://www.imini.tv/xingjunwenhua/wap_pro/",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Cache-Control": "max-age=0",
}

# 定义不同商品组
PRODUCT_GROUPS = {
    "DAISUKE": {
        "products": {
            "58153625": "【大陆地址】DAISUKE",
            "58153665": "【非大陆地址】DAISUKE",
        },
        "log_file": "phdaisuke.txt"
    },
    "JUWON": {
        "products": {
            "58153610": "【大陆地址】JUWON",
            "58153595": "【非大陆地址】JUWON",
        },
        "log_file": "phjuwon.txt"
    },
    "CHIHEN": {
        "products": {
            "58153582": "【大陆地址】CHIHEN",
            "58153587": "【非大陆地址】CHIHEN",
        },
        "log_file": "phchihen.txt"
    },
    "SHUAIBO": {
        "products": {
            "58153580": "【大陆地址】YUQI视频",
            "58153575": "【非大陆地址】YUQI视频",
        },
        "log_file": "phshuaibo.txt"
    },
    "STEVEN": {
        "products": {
            "58153487": "【大陆地址】SHUHUA视频",
            "58153471": "【非大陆地址】SHUHUA视频",
        },
        "log_file": "phsteven.txt"
    },
    "JL": {
        "products": {
            "58153465": "【大陆地址】JL",
            "58153467": "【非大陆地址】JL",
        },
        "log_file": "phjl.txt"
    },
    "HAN ": {
        "products": {
            "58153727": "【大陆地址】HAN ",
            "58153732": "【非大陆地址】HAN ",
        },
        "log_file": "phhan.txt"
    },
    "WOONGKI ": {
        "products": {
            "58153719": "【大陆地址】WOONGKI ",
            "58153706": "【非大陆地址】WOONGKI ",
        },
        "log_file": "phwoongki.txt"
    },
    "JEONGWOO ": {
        "products": {
            "58153675": "【大陆地址】JEONGWOO ",
            "58153689": "【非大陆地址】JEONGWOO ",
        },
        "log_file": "phjeongwoo.txt"
    }
}

# 基础监控间隔(秒)和随机波动范围
BASE_INTERVAL = 0.1  # 基础间隔时间
INTERVAL_JITTER = 0.05  # 随机波动范围(±)
MAX_CONSECUTIVE_ERRORS = 5  # 最大连续错误次数
ERROR_DELAY = 5  # 发生错误后的延迟时间(秒)


def get_random_headers():
    """生成随机请求头，模拟不同浏览器和设备"""
    headers = BASE_HEADERS.copy()

    # 从预定义列表中随机选择User-Agent
    headers["User-Agent"] = random.choice(USER_AGENTS)

    # 随机添加一些可选头信息
    if random.random() > 0.3:  # 70%概率添加此头
        headers["DNT"] = "1" if random.random() > 0.5 else "0"

    if random.random() > 0.5:  # 50%概率添加此头
        headers["Sec-Fetch-Dest"] = random.choice(["document", "empty"])
        headers["Sec-Fetch-Mode"] = random.choice(["navigate", "no-cors"])
        headers["Sec-Fetch-Site"] = random.choice(["same-origin", "none"])

    # 随机调整请求头顺序（有些网站会检查顺序）
    items = list(headers.items())
    random.shuffle(items)
    return dict(items)


class StockMonitor:
    def __init__(self, products, log_file):
        self.products = products
        self.log_file = log_file
        self.previous_stocks = {pro_id: None for pro_id in products}
        self.is_first_refresh = True
        self.consecutive_errors = 0  # 连续错误计数器

    def fetch_stock(self, pro_id):
        """获取商品库存，添加随机延迟和随机请求头"""
        try:
            # 在请求前添加随机小延迟，模拟人类操作
            time.sleep(random.uniform(0.1, 0.3))

            params = {
                "pro_id": pro_id,
                "t": int(time.time() * 1000)
            }

            # 使用随机请求头
            headers = get_random_headers()

            response = requests.get(
                API_URL,
                params=params,
                headers=headers,
                verify=False,
                # 使用随机超时时间
                timeout=random.uniform(8, 12)
            )

            # 随机小延迟模拟网络传输时间
            time.sleep(random.uniform(0.05, 0.2))

            response.raise_for_status()

            data = response.json()
            if data.get("code") == "200":
                self.consecutive_errors = 0  # 重置错误计数器
                return int(data["data"].get("pro_stock", 0))

            self.consecutive_errors += 1
            return None

        except Exception as e:
            self.consecutive_errors += 1
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 请求异常: {str(e)}")
            return None

    def format_output(self, all_stocks, all_changes):
        """生成标准化输出"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')
        output_parts = [current_time]
        for pro_id in self.products.keys():
            stock = all_stocks.get(pro_id, 0)
            change = all_changes.get(pro_id, 0)
            output_parts.extend([str(stock), str(change)])
        output = ','.join(output_parts)
        return output

    def log_data(self, all_stocks, all_changes):
        """记录所有商品数据到日志文件"""
        output = self.format_output(all_stocks, all_changes)
        print(output)
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(output + "\n")

    def monitor_once(self):
        """执行一次监控循环"""
        # 检查是否达到最大连续错误次数，如果是则暂停一段时间
        if self.consecutive_errors >= MAX_CONSECUTIVE_ERRORS:
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 连续错误次数过多，暂停 {ERROR_DELAY} 秒...")
            time.sleep(ERROR_DELAY)
            self.consecutive_errors = 0  # 重置错误计数器
            return

        all_stocks = {}
        all_changes = {}
        any_change = False

        # 遍历所有需要监控的商品
        for pro_id in self.products.keys():
            stock = self.fetch_stock(pro_id)

            if stock is not None:
                previous = self.previous_stocks[pro_id]
                change = stock - previous if previous is not None else 0
                all_stocks[pro_id] = stock
                all_changes[pro_id] = change

                if change != 0:
                    any_change = True

                self.previous_stocks[pro_id] = stock

        # 判断是否为第一次刷新或者有库存变化
        if self.is_first_refresh or any_change:
            self.log_data(all_stocks, all_changes)
            if self.is_first_refresh:
                print(f"[{self.log_file}] 首次获取商品库存，记录所有商品初始库存信息。")
            self.is_first_refresh = False


if __name__ == "__main__":
    # 创建所有监控器实例
    monitors = []
    for group_name, group_data in PRODUCT_GROUPS.items():
        print(f"初始化商品组: {group_name}")
        monitor = StockMonitor(group_data["products"], group_data["log_file"])
        monitors.append(monitor)
        for pid, name in group_data["products"].items():
            print(f"  {pid}: {name}")
        print(f"  日志文件: {group_data['log_file']}")
        print()

    try:
        while True:
            # 随机调整监控器顺序，避免固定模式
            random.shuffle(monitors)

            # 依次执行每个监控器
            for monitor in monitors:
                monitor.monitor_once()
                # 每个监控器之间添加随机延迟
                time.sleep(random.uniform(0.05, 0.2))

            # 计算带随机抖动的间隔时间
            sleep_time = BASE_INTERVAL + random.uniform(-INTERVAL_JITTER, INTERVAL_JITTER)
            # 确保间隔时间不会太短
            sleep_time = max(0.05, sleep_time)
            time.sleep(sleep_time)

    except KeyboardInterrupt:
        print("\n监控已手动停止")
