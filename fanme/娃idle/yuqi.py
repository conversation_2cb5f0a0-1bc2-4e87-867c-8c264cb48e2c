import requests
import time
import random
from datetime import datetime
import urllib3
from retrying import retry  # 需要安装retrying库：pip install retrying

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API配置
api_url = 'https://www.fanmeofficial.com/api/merchants/676a73a4b4857d0045b9424a/products/6878736fce1e190010b499a2/check_stock?variation_id=6878736fc3dee300177ab0ee'
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Origin': 'https://www.fanmeofficial.com',
    'Referer': 'https://www.fanmeofficial.com/products/fanmeet-8800287294190',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'Connection': 'keep-alive',
    'Sec-GPC': '1',
    'TE': 'trailers'
}

# 全局配置
file_path = 'yuqi.txt'
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/109.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 13_2) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15'
]

class FanmeStockMonitor:
    def __init__(self):
        self.prev_stock = None
        self.first_record = True  # 首次记录标志

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def fetch_fanme_stock(self):
        """获取Fanme库存数据（含指数退避重试）"""
        try:
            headers_copy = headers.copy()
            headers_copy['User-Agent'] = random.choice(user_agents)

            response = requests.get(api_url, headers=headers_copy, verify=False, timeout=10)
            response.raise_for_status()

            data = response.json()

            # 根据实际API响应结构解析库存
            stock = data.get('quantity', 0)
            if not isinstance(stock, int):
                stock = int(stock)

            # 数据校验：库存不能为负
            if stock < 0:
                raise ValueError(f"Invalid stock value: {stock}")

            return stock
        except Exception as e:
            print(f"[Fanme API Error] {str(e)}")
            raise

    def get_changes(self, current, previous):
        """计算有效变化量"""
        if previous is None:  # 首次获取
            return current, 0
        return current, current - previous

    def safe_fetch(self, fetch_func, prev_value):
        """带熔断机制的数据获取"""
        try:
            return fetch_func(), True
        except Exception as e:
            print(f"[Data Fetch Failed] Using cached value. Error: {str(e)}")
            return prev_value, False

    def log_data(self, current_time, stock, change):
        """记录数据到文件"""
        log_entry = f"{current_time},{stock},{change},0,0"
        print(log_entry)
        with open(file_path, 'a', buffering=1, encoding='utf-8') as f:
            f.write(log_entry + '\n')

    def run(self):

        while True:
            # 随机间隔（0.5-2秒）降低检测概率
            time.sleep(random.uniform(0.5, 2))

            # 获取当前时间（统一时间戳）
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

            # 获取Fanme数据（失败时使用缓存值）
            stock, stock_valid = self.safe_fetch(self.fetch_fanme_stock, self.prev_stock)

            # 计算变化量
            stock, stock_change = self.get_changes(stock, self.prev_stock)

            # 更新缓存值（仅当数据有效时）
            if stock_valid:
                self.prev_stock = stock

            # 首次记录逻辑
            if self.first_record:
                if stock_valid:  # 只要有有效数据
                    self.log_data(current_time, stock or 0, stock_change)
                    self.first_record = False  # 关闭首次记录标志

            # 记录条件：有有效数据源且有变化
            elif stock_valid and stock_change != 0:
                self.log_data(current_time, stock, stock_change)

            # 动态调整请求频率
            global_sleep = 15  # 基础等待时间15秒
            if not stock_valid:
                global_sleep = 30  # API失败时延长等待

            time.sleep(global_sleep)

def main():
    # 创建日志文件（带表头）
    with open(file_path, 'a', encoding='utf-8') as f:
        if f.tell() == 0:
            f.write("timestamp,stock,change,0,0\n")

    monitor = FanmeStockMonitor()
    monitor.run()

if __name__ == "__main__":
    main()