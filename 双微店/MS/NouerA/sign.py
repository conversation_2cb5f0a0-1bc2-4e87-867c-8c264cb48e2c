import requests
import time
from datetime import datetime


def get_stock(item_id):
    url = f'https://thor.weidian.com/detail/getItemSkuInfo/1.0?param={{"itemId":"{item_id}"}}&wdtoken=a826cb51&_=1724416335783'
    headers = {
        'accept': 'application/json, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8; v-components/tencent-live-plugin@wfr=BuyercopyURL; visitor_id=0e251d80-eeea-4432-97eb-99ac0929c8a5; __spider__sessionid=21083d741d133ba3; v-components/clean-up-advert@private_domain=1382036085; v-components/clean-up-advert@wx_app=1382036085',
        'origin': 'https://shop1649976533.v.weidian.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://shop1649976533.v.weidian.com/item.html?itemID=7436033956&wfr=c&source=goods_home&ifr=itemdetail&sfr=app',
        'sec-ch-ua': '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        return data.get('result', {}).get('itemStock')

    except Exception as e:
        print(f"Error retrieving stock data: {e}")
        return None


# URLs with item IDs
item_ids = ["7506547967",'7508345584']

# Previous stock values
previous_stocks = {}

while True:
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    current_stocks = {}
    stock_changes = {}

    # Retrieve the current stock for each item ID
    for item_id in item_ids:
        stock = get_stock(item_id)
        if stock is not None:
            current_stocks[item_id] = stock
            if item_id in previous_stocks:
                change = stock - previous_stocks[item_id]
                if change != 0:
                    stock_changes[item_id] = (stock, change)
            else:
                stock_changes[item_id] = (stock, 0)  # Initial stock check

            # Update previous stock
            previous_stocks[item_id] = stock

    # Prepare output only if there are stock changes
    if stock_changes:
        output = [timestamp + ':']
        for item_id in item_ids:
            if item_id in stock_changes:
                stock, change = stock_changes[item_id]
            else:
                stock = current_stocks.get(item_id, 0)
                change = 0
            output.extend([str(stock), str(change)])

        # 添加两个额外的0
        output.extend(['0', '0'])

        output_str = ', '.join(output)

        # Print and log the output
        print(output_str)
        with open('nouerA_sign.txt', 'a') as file:
            file.write(output_str + '\n')

    # Sleep for 7 seconds before the next check
    time.sleep(30)