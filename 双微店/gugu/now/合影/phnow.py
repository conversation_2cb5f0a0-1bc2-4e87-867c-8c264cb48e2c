import requests
import time

login_url = "https://api.kmsservice.cn/app-api/member/auth/email-login"
add_cart_url = "https://api.kmsservice.cn/app-api/trade/cart/add"
get_cart_url = "https://api.kmsservice.cn/app-api/trade/cart/list"
update_cart_url = "https://api.kmsservice.cn/app-api/trade/cart/update-count"
settlement_url = "https://api.kmsservice.cn/app-api/trade/order/settlement"

email = "<EMAIL>"
password = "177968753gu"
sku_list = [557, 558, 559, 560, 561]
address_id = 3259  # 请替换成你真实的有效地址ID

headers = {
    "Content-Type": "application/json",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Accept": "*/*",
    "User-Agent": ("Mozilla/5.0 (iPhone; CPU iPhone OS 18_2_1 like Mac OS X) "
                   "AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Html5Plus/1.0 (Immersed/62) uni-app"),
    "Accept-Language": "zh-CN,zh-Hans;q=0.9"
}

def login():
    payload = {"email": email, "password": password}
    resp = requests.post(login_url, json=payload, headers=headers)
    data = resp.json()
    if data.get("code") == 0:
        token = data["data"]["accessToken"]
        print("登录成功，token:", token)
        return token
    else:
        print("登录失败:", data)
        return None

def add_to_cart(token, sku_id):
    headers_auth = headers.copy()
    headers_auth["Authorization"] = f"Bearer {token}"
    payload = {"skuId": sku_id, "count": 1}
    resp = requests.post(add_cart_url, json=payload, headers=headers_auth)
    data = resp.json()
    if data.get("code") == 0:
        cart_item_id = data["data"]
        print(f"加入购物车成功 sku_id={sku_id}, cart_item_id={cart_item_id}")
        return cart_item_id
    else:
        print(f"加入购物车失败 sku_id={sku_id}:", data)
        return None

def get_cart_list(token):
    headers_auth = headers.copy()
    headers_auth["Authorization"] = f"Bearer {token}"
    resp = requests.get(get_cart_url, headers=headers_auth)
    data = resp.json()
    if data.get("code") == 0:
        return data["data"]
    else:
        print("获取购物车列表失败:", data)
        return None

def find_cart_item(cart_data, sku_id):
    for item in cart_data.get("validList", []):
        sku = item.get("sku", {})
        if sku.get("id") == sku_id:
            return item
    return None

def check_stock_with_settlement(token, cart_item_id, sku_id, spu_id, max_try):
    headers_auth = headers.copy()
    headers_auth["Authorization"] = f"Bearer {token}"

    low, high = 1, max_try
    stock = 0

    while low <= high:
        mid = (low + high) // 2
        payload = {
            "_isPass": True,
            "addressId": address_id,
            "items": [{
                "skuId": sku_id,
                "count": mid,
                "spuId": spu_id,
                "cartId": cart_item_id
            }],
            "pointStatus": False,
            "deliveryType": 1
        }

        try:
            resp = requests.post(settlement_url, headers=headers_auth, json=payload, timeout=10)
            data = resp.json()
        except Exception as e:
            print(f"请求异常: {e}, 重试中...")
            time.sleep(1)
            continue

        print(f"SKU {sku_id} 尝试数量: {mid}, 结算返回: {data}")

        if data.get("code") == 0:
            # 成功，库存至少 mid 个
            stock = mid
            low = mid + 1
        elif data.get("code") == 1008006004 or "库存不足" in (data.get("msg") or ""):
            # 库存不足，减少尝试数量
            high = mid - 1
        elif data.get("code") == 1011003000:
            # 价格异常，可能数量过大，缩小high继续尝试
            # 把high缩小为mid-1继续查
            print(f"支付价格异常，数量可能过大，缩小high")
            high = mid - 1
        else:
            # 其他异常，打印并停止
            print(f"未知异常，停止检测，返回: {data}")
            break

        time.sleep(0.1)

    return stock

if __name__ == "__main__":
    token = login()
    if not token:
        exit(1)

    # 加入购物车
    cart_items = {}
    for sku_id in sku_list:
        cart_id = add_to_cart(token, sku_id)
        if cart_id:
            cart_items[sku_id] = cart_id
        else:
            print(f"SKU {sku_id} 加入购物车失败，跳过后续检测")

    cart_data = get_cart_list(token)
    if not cart_data:
        exit(1)
    print("购物车列表原始数据:", cart_data)

    for sku_id in sku_list:
        cart_item = find_cart_item(cart_data, sku_id)
        if not cart_item:
            print(f"购物车无 SKU {sku_id}，无法查询库存")
            continue
        cart_id = cart_item.get("id")
        spu_id = cart_item.get("spu", {}).get("id")
        if not spu_id:
            print(f"找不到 SKU {sku_id} 对应的 spuId，跳过")
            continue

        # 取购物车中sku的库存作为最大尝试数量，避免过大
        sku_stock = cart_item.get("sku", {}).get("stock", 10000)
        max_try = min(sku_stock, 100000)  # 限制最大尝试量最多10万，避免接口异常

        stock = check_stock_with_settlement(token, cart_id, sku_id, spu_id, max_try)
        print(f"SKU ID {sku_id} 的真实库存（结算接口确认）是：{stock}")
