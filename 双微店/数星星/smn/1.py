import requests
import time
import random
from datetime import datetime
from collections import deque

def get_random_headers():
    """生成随机请求头，模拟不同浏览器和设备"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/127.0.0.0 Safari/537.36'
    ]

    headers = {
        'accept': 'application/json, */*',
        'accept-language': random.choice(['zh-CN,zh;q=0.9', 'zh-CN,zh;q=0.8,en;q=0.6', 'zh-CN,zh;q=0.7,en-US;q=0.3']),
        'cache-control': random.choice(['no-cache', 'max-age=0']),
        'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8; v-components/tencent-live-plugin@wfr=BuyercopyURL; visitor_id=0e251d80-eeea-4432-97eb-99ac0929c8a5; __spider__sessionid=21083d741d133ba3; v-components/clean-up-advert@private_domain=1382036085; v-components/clean-up-advert@wx_app=1382036085',
        'origin': 'https://shop1649976533.v.weidian.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://shop1649976533.v.weidian.com/item.html?itemID=7436033956&wfr=c&source=goods_home&ifr=itemdetail&sfr=app',
        'sec-ch-ua': random.choice([
            '"Not)A;Brand";v="99", "Google Chrome";v="127", "Chromium";v="127"',
            '"Microsoft Edge";v="126", "Chromium";v="126", "Not.A/Brand";v="24"',
            '"Mozilla Firefox";v="128", "rv:128.0", "Gecko/20100101"'
        ]),
        'sec-ch-ua-mobile': random.choice(['?0', '?1']),
        'sec-ch-ua-platform': random.choice(['"Windows"', '"Android"', '"iOS"']),
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': random.choice(user_agents)
    }
    return headers


def get_stock(item_id, max_retries=3):
    """获取库存信息，带有重试机制和随机延迟"""
    # 生成随机时间戳参数，避免缓存
    timestamp = int(time.time() * 1000)
    url = f'https://thor.weidian.com/detail/getItemSkuInfo/1.0?param={{"itemId":"{item_id}"}}&wdtoken=a826cb51&_={timestamp}'

    for retry in range(max_retries):
        try:
            # 随机小延迟，模拟人类操作思考时间
            time.sleep(random.uniform(0.2, 0.8))

            headers = get_random_headers()
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # 模拟人类阅读时间
            time.sleep(random.uniform(0.1, 0.5))

            data = response.json()
            return data.get('result', {}).get('itemStock')

        except Exception as e:
            print(f"尝试 {retry+1}/{max_retries} 获取 {item_id} 失败: {e}")
            if retry < max_retries - 1:
                # 指数退避重试
                sleep_time = (2 ** retry) + random.uniform(0, 1)
                time.sleep(sleep_time)

    print(f"获取 {item_id} 达到最大重试次数，放弃")
    return None


# 配置参数 - 可根据需要调整
min_refresh_interval = 5 # 最小刷新间隔（秒）
max_refresh_interval =7 # 最大刷新间隔（秒）
stats_interval = 60  # 性能统计间隔（秒）

# 监控的商品ID
item_ids = ["7515532088", "7515486480"]

# 存储上一次的库存值
previous_stocks = {}

# 性能统计变量
total_requests = 0
request_intervals = deque(maxlen=50)  # 保存最近50次请求间隔
start_time = time.time()
last_request_time = time.time()
last_stats_time = time.time()  # 上次统计时间

print("开始监控库存变化...")
print(f"刷新时间范围: {min_refresh_interval}-{max_refresh_interval}秒")
print("提示: 按Ctrl+C停止监控\n")

try:
    while True:
        loop_start = time.time()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        current_stocks = {}
        stock_changes = {}

        # 记录请求间隔
        current_interval = loop_start - last_request_time
        request_intervals.append(current_interval)
        last_request_time = loop_start

        # 获取每个商品的当前库存
        for item_id in item_ids:
            stock = get_stock(item_id)
            total_requests += 1  # 统计总请求数

            if stock is not None:
                current_stocks[item_id] = stock
                if item_id in previous_stocks:
                    change = stock - previous_stocks[item_id]
                    if change != 0:
                        stock_changes[item_id] = (stock, change)
                else:
                    # 首次检查
                    stock_changes[item_id] = (stock, 0)

                # 更新上一次的库存
                previous_stocks[item_id] = stock

        # 只有库存有变化时才输出和记录（保持原有输出逻辑）
        if stock_changes:
            output = [timestamp + ':']
            for item_id in item_ids:
                if item_id in stock_changes:
                    stock, change = stock_changes[item_id]
                else:
                    stock = current_stocks.get(item_id, 0)
                    change = 0
                output.extend([str(stock), str(change)])


            output_str = ', '.join(output)

            # 打印并记录（保持原有输出方式）
            print(output_str)
            with open('single.txt', 'a') as file:
                file.write(output_str + '\n')

        # 每分钟更新一次性能统计
        current_time = time.time()
        if current_time - last_stats_time >= stats_interval:
            if request_intervals:
                avg_interval = sum(request_intervals) / len(request_intervals)
                elapsed_time = current_time - start_time
                rpm = (total_requests / elapsed_time) * 60 if elapsed_time > 0 else 0
                print(f"\n[性能统计] 平均请求间隔: {avg_interval:.2f}秒 | 每分钟请求数: {rpm:.1f}")

            # 更新上次统计时间
            last_stats_time = current_time

        # 计算随机刷新时间（在配置的范围内）
        sleep_time = random.uniform(min_refresh_interval, max_refresh_interval)

        # 应用休眠
        time.sleep(sleep_time)

except KeyboardInterrupt:
    print("\n监控已手动停止")
except Exception as e:
    print(f"发生意外错误: {e}")
