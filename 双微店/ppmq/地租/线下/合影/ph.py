
import requests
import time
import random
from datetime import datetime
import urllib3
import threading
import queue
from retrying import retry
from collections import deque

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

ITEM_IDS = ["7522033211", "7523980288"]
IDOL_LIST = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "H<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "J<PERSON><PERSON><PERSON>"]
FILE_PATHS = {idol.lower(): f"ph{idol.lower()}.txt" for idol in IDOL_LIST}
CHECK_INTERVAL_MIN =1.5
CHECK_INTERVAL_MAX =1.5
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36"
]

previous_stocks = {idol: {item_id: None for item_id in ITEM_IDS} for idol in IDOL_LIST}
first_records = {idol: True for idol in IDOL_LIST}
log_queue = queue.Queue()
request_intervals = deque(maxlen=50)
total_requests = 0
start_time = time.time()
previous_data = {idol: {"kms": None, "weidian": None} for idol in IDOL_LIST}


def retry_if_exception(exception):
    return isinstance(exception, (requests.exceptions.RequestException, ConnectionError))


@retry(retry_on_exception=retry_if_exception, stop_max_attempt_number=3, wait_fixed=1000)
def get_stock(item_id):
    timestamp = int(time.time() * 1000)
    url = f'https://thor.weidian.com/detail/getItemSkuInfo/1.0?param={{"itemId":"{item_id}"}}&wdtoken=a826cb51&_={timestamp}'
    headers = {
        'user-agent': random.choice(USER_AGENTS),
        'referer': f'https://shop1649976533.v.weidian.com/item.html?itemID={item_id}&wfr=c',
    }
    time.sleep(random.uniform(0.1, 0.3))
    response = requests.get(url, headers=headers, timeout=8, verify=False)
    response.raise_for_status()
    data = response.json()
    sku_infos = data.get('result', {}).get('skuInfos', [])
    if sku_infos:
        return {
            idol: sum(
                sku['skuInfo']['stock']
                for sku in sku_infos
                if idol in sku['skuInfo'].get('title', '').upper()
            )
            for idol in IDOL_LIST
        }
    else:
        return data.get('result', {}).get('itemStock', 0)


def log_writer():
    while True:
        try:
            idol, log_entry = log_queue.get(timeout=1)
            file_path = FILE_PATHS.get(idol, "combined_log.txt")
            with open(file_path, 'a', encoding='utf-8', buffering=1) as f:
                f.write(log_entry + '\n')
            log_queue.task_done()
        except queue.Empty:
            continue


def process_idol_stocks(idol, timestamp, allow_first_record=True):
    global previous_stocks, first_records
    stock1 = previous_stocks[idol][ITEM_IDS[0]]
    stock2 = previous_stocks[idol][ITEM_IDS[1]]
    prev = previous_data[idol]
    if first_records[idol]:
        kms_change = 0
        wd_change = 0
        first_records[idol] = False
        need_record = allow_first_record
    else:
        kms_change = stock1 - (prev["kms"] or 0) if prev["kms"] is not None else 0
        wd_change = stock2 - (prev["weidian"] or 0) if prev["weidian"] is not None else 0
        need_record = kms_change != 0 or wd_change != 0

    previous_data[idol]["kms"] = stock1
    previous_data[idol]["weidian"] = stock2

    if need_record:
        log_entry = f"{timestamp}:,{stock1},{kms_change},{stock2},{wd_change}"
        print(f"[{idol}] {log_entry}")
        log_queue.put((idol.lower(), log_entry))
        return True
    return False


def monitor_stocks():
    global total_requests
    print("监控程序已启动")
    print("[系统] 正在初始化库存数据...")
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    for item_id in ITEM_IDS:
        try:
            stock_data = get_stock(item_id)
            if isinstance(stock_data, dict):
                for idol in IDOL_LIST:
                    previous_stocks[idol][item_id] = stock_data.get(idol, 0)
            else:
                for idol in IDOL_LIST:
                    previous_stocks[idol][item_id] = stock_data
        except Exception as e:
            for idol in IDOL_LIST:
                previous_stocks[idol][item_id] = 0

    for idol in IDOL_LIST:
        stock1 = previous_stocks[idol][ITEM_IDS[0]]
        stock2 = previous_stocks[idol][ITEM_IDS[1]]
        previous_data[idol]["kms"] = stock1
        previous_data[idol]["weidian"] = stock2
        log_entry = f"{timestamp}:,{stock1},0,{stock2},0"
        print(f"[{idol}] {log_entry}")
        log_queue.put((idol.lower(), log_entry))

    print("[系统] 开始定期库存监控...")

    first_monitor_loop = True

    while True:
        loop_start = time.time()
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        for item_id in ITEM_IDS:
            try:
                stock_data = get_stock(item_id)
                if isinstance(stock_data, dict):
                    for idol in IDOL_LIST:
                        previous_stocks[idol][item_id] = stock_data.get(idol, previous_stocks[idol][item_id])
            except Exception:
                continue

        for idol in IDOL_LIST:
            process_idol_stocks(idol, timestamp, allow_first_record=not first_monitor_loop)

        first_monitor_loop = False
        total_requests += 1
        request_intervals.append(time.time() - loop_start)
        time.sleep(random.uniform(CHECK_INTERVAL_MIN, CHECK_INTERVAL_MAX))


if __name__ == "__main__":
    threading.Thread(target=log_writer, daemon=True).start()
    monitor_stocks()
