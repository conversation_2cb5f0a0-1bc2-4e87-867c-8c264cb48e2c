import requests
import time
import logging
import json
import random
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
import os

class InventoryMonitor:
    def __init__(self, config_path: str = 'config.json'):
        """初始化库存监控器，从配置文件加载参数"""
        # 先初始化默认配置，确保logger可以使用
        self.config = {
            "api_url": "https://www.musicndrama.com/ajax/oms/OMS_get_product.cm?prod_idx=11146",
            "referer": "https://www.musicndrama.com/shop_view/?idx=11146",
            "cookies": {"al": "KR"},
            "request_interval": (8, 15),
            "max_retries": 3,
            "retry_delay": 5,
            "log_file": self._get_default_log_path(),
            "user_agents": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            ],
            "log_level": "INFO"
        }
        self.logger = logging.getLogger('inventory_monitor')
        self.setup_logger()

        # 现在可以安全地加载配置文件
        try:
            file_config = self._load_config(config_path)
            self.config.update(file_config)  # 用文件配置覆盖默认配置
        except Exception as e:
            self.logger.warning(f"使用默认配置: {e}")

        self.prev_stock = None
        self.first_run = True
        self.session = requests.Session()
        self._configure_session()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """从配置文件加载爬虫参数"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise  # 让调用者处理文件不存在的情况
        except Exception as e:
            self.logger.error(f"解析配置文件失败: {e}")
            return {}

    def _get_default_log_path(self) -> str:
        """获取默认日志文件路径，确保在可写目录"""
        try:
            # 尝试使用当前工作目录
            return os.path.join(os.getcwd(), 'pow.txt')
        except Exception:
            # 回退到临时目录
            return os.path.join(os.getenv('TEMP', '/tmp'), 'pow.txt')

    def setup_logger(self) -> None:
        """配置日志记录器"""
        log_level = self.config.get('log_level', 'INFO')
        self.logger.setLevel(getattr(logging, log_level))

        if not self.logger.handlers:
            try:
                log_file = self.config.get('log_file', self._get_default_log_path())

                # 创建日志目录
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)

                # 检查文件是否可写
                try:
                    with open(log_file, 'a'):
                        pass
                    self.logger.info(f"日志文件将写入: {log_file}")
                except Exception as e:
                    self.logger.critical(f"无法写入日志文件 {log_file}: {e}")
                    log_file = self._get_default_log_path()
                    self.logger.warning(f"使用替代日志路径: {log_file}")

                # 创建文件处理器 - 只记录数据相关日志
                file_handler = logging.FileHandler(log_file)
                file_handler.setLevel(logging.INFO)

                # 创建控制台处理器 - 记录所有日志
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)

                # 定义文件日志格式（只记录数据）
                file_formatter = logging.Formatter('%(asctime)s:,%(message)s', datefmt='%Y-%m-%d %H:%M:%S')
                file_handler.setFormatter(file_formatter)

                # 定义控制台日志格式（包含详细信息）
                console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                console_handler.setFormatter(console_formatter)

                # 添加过滤器，控制哪些日志写入文件
                class DataFilter(logging.Filter):
                    def filter(self, record):
                        # 只允许特定格式的消息写入文件
                        # 格式应为: 库存值,差值,0,0
                        if isinstance(record.msg, str) and len(record.msg.split(',')) >= 2:
                            return True
                        return False

                file_handler.addFilter(DataFilter())

                self.logger.addHandler(file_handler)
                self.logger.addHandler(console_handler)

            except Exception as e:
                self.logger.critical(f"配置日志处理器失败: {e}")
                # 添加默认控制台处理器以确保日志不会丢失
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
                self.logger.addHandler(console_handler)

    def _configure_session(self) -> None:
        """配置请求会话，设置请求头和Cookie"""
        user_agents = self.config.get('user_agents')
        user_agent = random.choice(user_agents) if user_agents and isinstance(user_agents, list) else 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0'

        referer = self.config.get('referer', 'https://www.musicndrama.com/shop_view/?idx=10854')

        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': referer,
            'Connection': 'keep-alive'
        })

        cookies = self.config.get('cookies', {"al": "KR"})
        self.session.cookies.update(cookies)

    def _get_random_interval(self) -> float:
        """获取随机请求间隔时间"""
        interval = self.config.get('request_interval', (8, 15))
        min_interval, max_interval = interval
        return random.uniform(min_interval, max_interval)

    def fetch_inventory(self) -> Optional[int]:
        """获取当前库存数据"""
        for attempt in range(self.config.get('max_retries', 3)):
            try:
                api_url = self.config.get('api_url', 'https://www.musicndrama.com/ajax/oms/OMS_get_product.cm?prod_idx=10854')

                response = self.session.get(
                    api_url,
                    timeout=10,
                    headers={'Cache-Control': 'no-cache'}
                )
                response.raise_for_status()

                if 'application/json' not in response.headers.get('Content-Type', ''):
                    self.logger.warning(f"非JSON响应: {response.text[:100]}...")
                    return None

                data = response.json()
                return self._parse_inventory_data(data)

            except requests.exceptions.RequestException as e:
                retry_delay = self.config.get('retry_delay', 5)
                self.logger.error(f"请求失败 (尝试 {attempt+1}/{self.config.get('max_retries', 3)}): {e}")
                if attempt < self.config.get('max_retries', 3) - 1:
                    time.sleep(retry_delay)
            except (ValueError, KeyError, IndexError) as e:
                self.logger.error(f"数据解析失败: {e}")
                return None

        self.logger.error(f"达到最大重试次数 ({self.config.get('max_retries', 3)})")
        return None

    def _parse_inventory_data(self, data: Dict[str, Any]) -> Optional[int]:
        """解析库存数据，增加防御性检查"""
        options_detail = data.get('data', {}).get('options_detail')

        if not options_detail:
            self.logger.warning("找不到库存数据: options_detail为空")
            return None

        if not isinstance(options_detail, list) or len(options_detail) == 0:
            self.logger.warning(f"options_detail格式异常: {type(options_detail)}")
            return None

        first_item = options_detail[0]
        stock = first_item.get('stock')

        if stock is None:
            self.logger.warning(f"找不到库存字段: {list(first_item.keys())}")
            return None

        try:
            return int(stock)
        except (ValueError, TypeError):
            self.logger.warning(f"库存值类型异常: {type(stock)}, 值: {stock}")
            return None

    def process_inventory_change(self, current_stock: int) -> None:
        """处理库存变化"""
        if self.first_run:
            # 记录数据到文件
            self.logger.info(f"{current_stock},0,0,0")
            # 仅在控制台显示详细信息
            self.first_run = False
        elif current_stock != self.prev_stock:
            diff = current_stock - self.prev_stock
            # 记录数据到文件
            self.logger.info(f"{current_stock},{diff},0,0")
            # 仅在控制台显示详细信息

        self.prev_stock = current_stock

    def run(self) -> None:
        """运行库存监控主循环"""
        # 仅在控制台输出服务启动信息，不写入文件

        try:
            while True:
                start_time = time.time()
                current_stock = self.fetch_inventory()

                if current_stock is not None:
                    self.process_inventory_change(current_stock)
                else:
                    self.logger.warning("获取库存失败，将重试")

                elapsed = time.time() - start_time
                wait_time = max(0, self._get_random_interval() - elapsed)
                self.logger.debug(f"等待 {wait_time:.2f} 秒后进行下一次请求")
                time.sleep(wait_time)

        except KeyboardInterrupt:
            self.logger.info("监控服务已手动停止")
        except Exception as e:
            self.logger.critical(f"未处理的异常: {e}", exc_info=True)
        finally:
            self.session.close()

if __name__ == "__main__":
    monitor = InventoryMonitor()
    monitor.run()