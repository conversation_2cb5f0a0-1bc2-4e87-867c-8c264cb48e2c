import requests
import time
import random
from datetime import datetime
import urllib3
import threading
import queue
import concurrent.futures

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ================== 配置区域 ==================
LOGIN_URL = "https://api.kmsservice.cn/app-api/member/auth/email-login"
ADD_CART_URL = "https://api.kmsservice.cn/app-api/trade/cart/add"
GET_CART_URL = "https://api.kmsservice.cn/app-api/trade/cart/list"
SETTLEMENT_URL = "https://api.kmsservice.cn/app-api/trade/order/settlement"

EMAIL = "<EMAIL>"
PASSWORD = "abc123456"
ADDRESS_ID = 3259  # 请替换成有效地址ID

AUTH_HEADERS = {
    "Content-Type": "application/json",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Accept": "*/*",
    "User-Agent": ("Mozilla/5.0 (iPhone; CPU iPhone OS 18_2_1 like Mac OS X) "
                   "AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Html5Plus/1.0 (Immersed/62) uni-app"),
    "Accept-Language": "zh-CN,zh-Hans;q=0.9"
}

IDOL_SKUS = {
    "XIAOJUN": [546, 551],
    "KUN": [545, 550],
    "HENDERY": [544, 549],
    "TEN": [543, 548],
    "YANGYANG": [542, 547],
    "线下签售": [542,543,544,545,546,547,548,549,550,551],
}

IDOL_LIST = ["XIAOJUN", "KUN", "HENDERY", "TEN", "YANGYANG", "线下签售"]
FILE_PATHS = {
    "XIAOJUN": "xj.txt",
    "KUN": "kun.txt",
    "HENDERY": "hen.txt",
    "TEN": "ten.txt",
    "YANGYANG": "yy.txt",
    "线下签售": "wayv.txt"
}

KMS_URL = 'https://kms.kmstation.net/prod/prodInfo?prodId=3837'
KMS_HEADERS = {
    'locale': 'zh_CN',
    'Origin': 'http://page.kmstation.net',
    'Referer': 'http://page.kmstation.net/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

WEIDIAN_URL = 'https://thor.weidian.com/detail/getItemSkuInfo/1.0'
WEIDIAN_PARAMS = {'wdtoken': 'a826cb51'}
WEIDIAN_HEADERS = {
    'accept': 'application/json, /',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8',
    'origin': 'https://shop1382036085.v.weidian.com',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/********* Safari/537.36'
]

# ================== 全局变量 ==================
previous_data = {idol: {"kms": None, "weidian": None, "real": None} for idol in IDOL_LIST}
first_records = {idol: True for idol in IDOL_LIST}
log_queue = queue.Queue()
real_stock_cache = {idol: 0 for idol in IDOL_LIST}
real_stock_timestamps = {idol: 0 for idol in IDOL_LIST}

# ================== 登录函数 ==================
def login():
    payload = {"email": EMAIL, "password": PASSWORD}
    try:
        resp = requests.post(LOGIN_URL, json=payload, headers=AUTH_HEADERS, timeout=10)
        data = resp.json()
        if data.get("code") == 0:
            token = data["data"]["accessToken"]
            print("登录成功，token:", token)
            return token
        else:
            print("登录失败:", data)
            return None
    except Exception as e:
        print(f"登录请求异常: {e}")
        return None

# ================== 加购物车 ==================
def add_to_cart(token, sku_id):
    headers_auth = AUTH_HEADERS.copy()
    headers_auth["Authorization"] = f"Bearer {token}"
    payload = {"skuId": sku_id, "count": 1}
    try:
        resp = requests.post(ADD_CART_URL, json=payload, headers=headers_auth, timeout=10)
        data = resp.json()
        if data.get("code") == 0:
            cart_item_id = data["data"]
            return cart_item_id
        else:
            return None
    except Exception as e:
        return None

# ================== 获取购物车列表 ==================
def get_cart_list(token):
    headers_auth = AUTH_HEADERS.copy()
    headers_auth["Authorization"] = f"Bearer {token}"
    try:
        resp = requests.get(GET_CART_URL, headers=headers_auth, timeout=10)
        data = resp.json()
        if data.get("code") == 0:
            return data["data"]
        else:
            return None
    except Exception:
        return None

# ================== 购物车中查找sku项 ==================
def find_cart_item(cart_data, sku_id):
    for item in cart_data.get("validList", []):
        sku = item.get("sku", {})
        if sku.get("id") == sku_id:
            return item
    return None

# ================== 结算接口二分法查询真实库存 ==================
def check_stock_with_settlement(token, cart_item_id, sku_id, spu_id, max_try):
    headers_auth = AUTH_HEADERS.copy()
    headers_auth["Authorization"] = f"Bearer {token}"

    low, high = 1, max_try
    stock = 0

    while low <= high:
        mid = (low + high) // 2
        payload = {
            "_isPass": True,
            "addressId": ADDRESS_ID,
            "items": [{
                "skuId": sku_id,
                "count": mid,
                "spuId": spu_id,
                "cartId": cart_item_id
            }],
            "pointStatus": False,
            "deliveryType": 1
        }

        try:
            resp = requests.post(SETTLEMENT_URL, headers=headers_auth, json=payload, timeout=10)
            data = resp.json()
        except Exception:
            time.sleep(1)
            continue

        if data.get("code") == 0:
            stock = mid
            low = mid + 1
        elif data.get("code") == 1008006004 or "库存不足" in (data.get("msg") or ""):
            high = mid - 1
        elif data.get("code") == 1011003000:
            high = mid - 1
        else:
            break

        time.sleep(0.2)

    return stock

# ================== 更新购物车项数量（0删除）==================
def update_cart_item(token, cart_item_id, count=0):
    headers_auth = AUTH_HEADERS.copy()
    headers_auth["Authorization"] = f"Bearer {token}"
    payload = {"id": cart_item_id, "count": count}
    try:
        resp = requests.put("https://api.kmsservice.cn/app-api/trade/cart/update-count",
                            json=payload, headers=headers_auth, timeout=5)
        data = resp.json()
        if data.get("code") == 0:
            return True
        else:
            return False
    except Exception:
        return False

# ================== 并发检测单个SKU真实库存函数 ==================
def detect_idol_stock(token, idol, sku_id):
    cart_id = add_to_cart(token, sku_id)
    if not cart_id:
        return 0

    cart_data = get_cart_list(token)
    if not cart_data:
        update_cart_item(token, cart_id, 0)
        return 0

    cart_item = find_cart_item(cart_data, sku_id)
    if not cart_item:
        update_cart_item(token, cart_id, 0)
        return 0

    spu_id = cart_item.get("spu", {}).get("id")
    if not spu_id:
        update_cart_item(token, cart_id, 0)
        return 0

    max_try = cart_item.get("sku", {}).get("stock", 1000)
    real_stock = check_stock_with_settlement(token, cart_id, sku_id, spu_id, max_try)

    update_cart_item(token, cart_id, 0)
    return real_stock

# ================== 真实库存检测线程 ==================
def real_stock_detection():
    while True:
        try:
            token = login()
            if not token:
                print("登录失败，等待30秒后重试...")
                time.sleep(30)
                continue

            idol_stocks = {idol: 0 for idol in IDOL_LIST}

            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = []
                for idol in IDOL_LIST:
                    sku_list = IDOL_SKUS.get(idol, [])
                    if not sku_list:
                        continue
                    for sku_id in sku_list:
                        futures.append((idol, executor.submit(detect_idol_stock, token, idol, sku_id)))

                for idol, future in futures:
                    stock = future.result()
                    idol_stocks[idol] += stock

            for idol, stock in idol_stocks.items():
                real_stock_cache[idol] = stock
                real_stock_timestamps[idol] = time.time()

            time.sleep(15)
        except Exception as e:
            print(f"真实库存检测异常: {e}")
            time.sleep(15)

# ================== 其他API获取函数（KMS和微店） ==================
def fetch_all_kms():
    try:
        headers = KMS_HEADERS.copy()
        headers['User-Agent'] = random.choice(USER_AGENTS)
        random_param = f"nocache={random.randint(1000000, 9999999)}"
        full_url = f"{KMS_URL}&{random_param}"
        response = requests.get(full_url, headers=headers, verify=False, timeout=5)
        response.raise_for_status()
        sku_list = response.json().get('skuList', [])
        idol_stocks = {}
        for idol in IDOL_LIST:
            idol_stocks[idol] = sum(sku['stocks'] for sku in sku_list if idol in sku.get('skuName', '').upper())
        return idol_stocks, True
    except Exception as e:
        print(f"[KMS API Error] {str(e)}")
        return {idol: 0 for idol in IDOL_LIST}, False

def fetch_all_weidian():
    try:
        headers = WEIDIAN_HEADERS.copy()
        headers['User-Agent'] = random.choice(USER_AGENTS)
        params = WEIDIAN_PARAMS.copy()
        params['_'] = str(int(time.time() * 1000))
        params['param'] = f'{{"itemId":"7522155349"}}'  # 请替换实际商品ID
        response = requests.get(WEIDIAN_URL, headers=headers, params=params, timeout=5)
        response.raise_for_status()
        sku_infos = response.json()['result']['skuInfos']
        idol_stocks = {}
        for idol in IDOL_LIST:
            idol_stocks[idol] = sum(
                sku['skuInfo']['stock']
                for sku in sku_infos
                if idol in sku['skuInfo'].get('title', '').upper()
            )
        return idol_stocks, True
    except Exception as e:
        print(f"[Weidian API Error] {str(e)}")
        return {idol: 0 for idol in IDOL_LIST}, False

# ================== 日志写入线程 ==================
def log_writer():
    while True:
        try:
            idol, log_entry = log_queue.get(timeout=1)
            with open(FILE_PATHS[idol], 'a', buffering=1, encoding='utf-8') as f:
                f.write(log_entry + '\n')
            log_queue.task_done()
        except queue.Empty:
            time.sleep(0.1)
        except Exception as e:
            print(f"[Log Writer Error] {str(e)}")
            time.sleep(1)

# ================== 变化量计算 ==================
def calculate_changes(idol, current_kms, current_weidian, current_real):
    prev = previous_data[idol]

    if first_records[idol]:
        kms_change = 0
        wd_change = 0
        real_change = 0
        first_records[idol] = False
        need_record = True
    else:
        kms_change = current_kms - (prev["kms"] or 0) if prev["kms"] is not None else 0
        wd_change = current_weidian - (prev["weidian"] or 0) if prev["weidian"] is not None else 0

        if isinstance(current_real, int):
            real_change = current_real - (prev["real"] or 0) if prev["real"] is not None else 0
        else:
            real_change = 0

        need_record = kms_change != 0 or wd_change != 0 or real_change != 0

    previous_data[idol]["kms"] = current_kms
    previous_data[idol]["weidian"] = current_weidian
    if isinstance(current_real, int):
        previous_data[idol]["real"] = current_real

    return kms_change, wd_change, real_change, need_record

# ================== 库存监控主循环 ==================
def stock_monitor():
    while True:
        loop_start = time.time()

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

        all_kms_data, kms_valid = fetch_all_kms()
        all_weidian_data, wd_valid = fetch_all_weidian()

        for idol in IDOL_LIST:
            kms_stock = all_kms_data.get(idol, 0) if kms_valid else previous_data[idol]["kms"] or 0
            wd_stock = all_weidian_data.get(idol, 0) if wd_valid else previous_data[idol]["weidian"] or 0

            real_stock = real_stock_cache.get(idol, 0)
            if time.time() - real_stock_timestamps.get(idol, 0) > 120:
                real_stock = 0

            kms_change, wd_change, real_change, need_record = calculate_changes(idol, kms_stock, wd_stock, real_stock)

            # 只首次或者变化时才输出，修复日志格式（添加逗号分隔符）
            if need_record:
                log_entry = (f"{current_time},"
                             f"{kms_stock},{kms_change},"
                             f"{wd_stock},{wd_change},"
                             f"{real_stock},{real_change}")
                print(f"[{idol}] {log_entry}")
                log_queue.put((idol, log_entry))

        elapsed = time.time() - loop_start
        time.sleep(max(0, 1.65 - elapsed))

# ================== 主程序入口 ==================
if __name__ == "__main__":
    threading.Thread(target=log_writer, daemon=True).start()
    threading.Thread(target=real_stock_detection, daemon=True).start()
    stock_monitor()
