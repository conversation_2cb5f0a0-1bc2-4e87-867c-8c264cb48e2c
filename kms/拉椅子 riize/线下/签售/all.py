import requests
import time
import random
from datetime import datetime
import urllib3
import threading
import queue
import json
from retrying import retry  # 需要安装retrying库：pip install retrying
from collections import deque  # 添加deque用于计算平均请求间隔

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 全局配置
idol_list = ["ANTON", "EUNSEOK", "SHOTARO", "SOHEE", "SUNGCHAN", "WONBI<PERSON>",'线下签售'] # 所有偶像列表
file_paths = {
    "ANTON": "anton.txt",
    "EUNSEOK": "eunseok.txt",
    "SHOTARO": "shotaro.txt",
    "SOHEE": "sohee.txt",
    "SUNGCHAN": "sungchan.txt",
    "WONBIN": "wonbin.txt",
    '线下签售':'riize.txt'
}
item_ids = ["7485732413"] # 微店商品ID

# 请求配置
url_kms = 'https://kms.kmstation.net/prod/prodInfo?prodId=3771'
headers_kms = {
    'locale': 'zh_CN',
    'Origin': 'http://page.kmstation.net',
    'Referer': 'http://page.kmstation.net/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

url_weidian = 'https://thor.weidian.com/detail/getItemSkuInfo/1.0'
params_weidian = {
    'wdtoken': 'a826cb51',
    '_': str(int(time.time() * 1000))
}
headers_weidian = {
    'accept': 'application/json, /',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8',
    'origin': 'https://shop1382036085.v.weidian.com',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

# 随机UA池
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/********* Safari/537.36'
]

# 数据缓存
previous_data = {idol: {"kms": None, "weidian": None} for idol in idol_list}
first_records = {idol: True for idol in idol_list}

# 线程安全的队列，用于存放待写入的数据
log_queue = queue.Queue()

# 添加性能监控变量
last_request_time = time.time()
request_intervals = deque(maxlen=50)  # 保存最近50次请求的间隔
total_requests = 0
start_monitoring_time = time.time()

def fetch_all_kms():
    """获取所有偶像的KMS数据"""
    try:
        headers = headers_kms.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 添加随机参数避免缓存
        random_param = f"nocache={random.randint(1000000, 9999999)}"
        full_url = f"{url_kms}&{random_param}"

        response = requests.get(full_url, headers=headers, verify=False, timeout=5)
        response.raise_for_status()

        sku_list = response.json().get('skuList', [])

        # 提取每个偶像的库存数据
        idol_stocks = {}
        for idol in idol_list:
            idol_stocks[idol] = sum(sku['stocks'] for sku in sku_list if idol in sku.get('skuName', '').upper())

        return idol_stocks, True
    except Exception as e:
        print(f"[KMS API Error] {str(e)}")
        return {idol: 0 for idol in idol_list}, False

def fetch_all_weidian():
    """获取所有偶像的微店数据"""
    # 检查item_ids是否为空或仅包含空字符串
    if not item_ids or not item_ids[0].strip():
        # 如果ID为空，直接返回0库存，但不标记为错误
        # 返回True表示这是预期行为，而不是错误
        return {idol: 0 for idol in idol_list}, True

    try:
        headers = headers_weidian.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 更新时间戳参数
        params = params_weidian.copy()
        params['_'] = str(int(time.time() * 1000))
        params['param'] = f'{{"itemId":"{item_ids[0]}"}}'

        response = requests.get(url_weidian, headers=headers, params=params, timeout=5)
        response.raise_for_status()

        sku_infos = response.json()['result']['skuInfos']

        # 提取每个偶像的库存数据
        idol_stocks = {}
        for idol in idol_list:
            idol_stocks[idol] = sum(
                sku['skuInfo']['stock']
                for sku in sku_infos
                if idol in sku['skuInfo'].get('title', '').upper()
            )

        return idol_stocks, True
    except Exception as e:
        print(f"[Weidian API Error] {str(e)}")
        return {idol: 0 for idol in idol_list}, False

def log_writer():
    """从队列中获取数据并写入对应文件"""
    while True:
        try:
            idol, log_entry = log_queue.get(timeout=1)
            with open(file_paths[idol], 'a', buffering=1, encoding='utf-8') as f:
                f.write(log_entry + '\n')
            log_queue.task_done()
        except queue.Empty:
            time.sleep(0.1)
        except Exception as e:
            print(f"[Log Writer Error] {str(e)}")
            time.sleep(1)

def calculate_changes(idol, current_kms, current_weidian):
    """计算变化量并返回是否需要记录"""
    prev = previous_data[idol]

    # 首次记录逻辑
    if first_records[idol]:
        kms_change = 0
        wd_change = 0
        first_records[idol] = False
        need_record = True
    else:
        # 计算变化量
        kms_change = current_kms - (prev["kms"] or 0) if prev["kms"] is not None else 0
        wd_change = current_weidian - (prev["weidian"] or 0) if prev["weidian"] is not None else 0
        # 判断是否需要记录
        need_record = kms_change != 0 or wd_change != 0

    # 更新缓存
    previous_data[idol]["kms"] = current_kms
    previous_data[idol]["weidian"] = current_weidian

    return kms_change, wd_change, need_record

def print_performance_stats():
    """打印性能统计信息"""
    global total_requests, start_monitoring_time

    # 计算平均请求间隔
    avg_interval = sum(request_intervals) / len(request_intervals) if request_intervals else 0

    # 计算每分钟请求数
    elapsed_minutes = (time.time() - start_monitoring_time) / 60
    rpm = total_requests / elapsed_minutes if elapsed_minutes > 0 else 0

    print(f"[性能统计] 平均请求间隔: {avg_interval:.2f}秒 | 每分钟请求数: {rpm:.1f} | 总请求: {total_requests}")

def main():
    # 启动日志写入线程
    writer_thread = threading.Thread(target=log_writer, daemon=True)
    writer_thread.start()

    global last_request_time, total_requests
    last_stats_time = time.time()

    # 打印配置信息
    if not item_ids or not item_ids[0].strip():
        print("[配置] 微店商品ID未设置，将跳过微店API请求")

    # 主循环
    while True:
        start_time = time.time()

        # 更新请求间隔统计
        current_interval = start_time - last_request_time
        request_intervals.append(current_interval)
        last_request_time = start_time
        total_requests += 1

        # 每100次请求或每5分钟打印一次性能统计
        if total_requests % 100 == 0 or (time.time() - last_stats_time) > 300:
            print_performance_stats()
            last_stats_time = time.time()

        # 获取当前时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

        # 获取所有偶像数据（仅请求一次API）
        all_kms_data, kms_valid = fetch_all_kms()
        all_weidian_data, wd_valid = fetch_all_weidian()

        # 处理每个偶像的数据
        for idol in idol_list:
            kms_stock = all_kms_data.get(idol, 0) if kms_valid else previous_data[idol]["kms"] or 0
            wd_stock = all_weidian_data.get(idol, 0) if wd_valid else previous_data[idol]["weidian"] or 0

            # 计算变化量
            kms_change, wd_change, need_record = calculate_changes(idol, kms_stock, wd_stock)

            # 记录数据
            if need_record:
                log_entry = f"{current_time},{kms_stock},{kms_change},{wd_stock},{wd_change}"
                print(f"[{idol}] {log_entry}")
                log_queue.put((idol, log_entry))

        # 计算循环耗时，控制请求频率
        elapsed = time.time() - start_time

        # 基础间隔（设置为0.7-1秒之间）
        min_interval =0.5  # 最小间隔
        max_interval =0.8 # 最大间隔

        # 添加判断逻辑以动态调整频率
        if not kms_valid and not wd_valid:
            # 两个API都失败时，大幅增加间隔
            sleep_time = 5
            print("[Warning] 所有API请求失败，休眠5秒...")
        else:
            # 正常情况下，确保间隔在min_interval和max_interval之间
            # 如果处理时间已经超过了最小间隔，则使用一个很小的延迟
            if elapsed >= min_interval:
                sleep_time = 0.1  # 最小延迟，确保至少有一点点间隔
            else:
                # 计算需要的睡眠时间，保证总时间在min_interval和max_interval之间
                target_interval = random.uniform(min_interval, max_interval)
                sleep_time = max(0.1, target_interval - elapsed)

        # 应用休眠时间
        time.sleep(sleep_time)

if __name__ == "__main__":
    main()