import requests
import time
import random
from datetime import datetime
import urllib3
import threading
import queue
import json
from collections import deque  # 用于计算平均请求间隔

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 全局配置
idol_list = ["ANTON", "EUNSEOK", "<PERSON><PERSON>AR<PERSON>", "<PERSON><PERSON><PERSON>", "SUNGCHAN", "WONBIN"]  # 所有成员列表
file_paths = {
    "anton": "anton.txt",
    "eunseok": "eunseok.txt",
    "shotaro": "shotaro.txt",
    "sohee": "sohee.txt",
    "sungchan": "sungchan.txt",
    "wonbin": "wonbin.txt"
}

# 请求配置
url_kmonstar_tw = 'https://www.kmonstar.com.tw/products/%E6%87%89%E5%8B%9F-250829-riize-the-1st-album-odyssey-%E5%B0%88%E8%BC%AF%E7%99%BC%E8%A1%8C%E7%B4%80%E5%BF%B5%E7%B0%BD%E5%90%8D%E6%9C%83-in-taipei.json'
headers_kmonstar_tw = {
    'Accept': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Referer': 'https://www.kmonstar.com.tw/products/%E6%87%89%E5%8B%9F-250829-riize-the-1st-album-odyssey-%E5%B0%88%E8%BC%AF%E7%99%BC%E8%A1%8C%E7%B4%80%E5%BF%B5%E7%B0%BD%E5%90%8D%E6%9C%83-in-taipei'
}

url_kmonstar_org = 'https://kmonstar.org/api/v1/event/detail/3b9aa36d-c70e-4912-b999-925df05dc90f'
headers_kmonstar_org = {
    'Accept': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Referer': 'https://kmonstar.org/ko/eventproductdetail/3b9aa36d-c70e-4912-b999-925df05dc90f'
}

# 随机UA池
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/********* Safari/537.36'
]

# 数据缓存
previous_data = {idol: {"kmonstar_tw": None, "kmonstar_org": None} for idol in idol_list}
first_records = {idol: True for idol in idol_list}

# 线程安全的队列，用于存放待写入的数据
log_queue = queue.Queue()

# 添加性能监控变量
last_request_time = time.time()
request_intervals = deque(maxlen=50)  # 保存最近50次请求的间隔
total_requests = 0
start_monitoring_time = time.time()

def fetch_kmonstar_tw():
    """获取台湾K-MONSTAR网站的库存数据"""
    try:
        headers = headers_kmonstar_tw.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 添加随机参数避免缓存
        random_param = f"nocache={random.randint(1000000, 9999999)}"
        full_url = f"{url_kmonstar_tw}?{random_param}"

        response = requests.get(full_url, headers=headers, verify=False, timeout=5)
        response.raise_for_status()
        
        data = response.json()
        inventory_quantity = data.get('inventory_quantity', 0)
        
        # 由于API不区分成员，我们将总库存平均分配给每个成员
        # 实际应用中可能需要根据具体情况调整这个逻辑
        idol_stocks = {idol: abs(inventory_quantity) for idol in idol_list}
        
        return idol_stocks, True
    except Exception as e:
        print(f"[K-MONSTAR TW API Error] {str(e)}")
        return {idol: 0 for idol in idol_list}, False

def fetch_kmonstar_org():
    """获取韩国K-MONSTAR网站的销售数据"""
    try:
        headers = headers_kmonstar_org.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 添加随机参数避免缓存
        random_param = f"nocache={random.randint(1000000, 9999999)}"
        full_url = f"{url_kmonstar_org}?{random_param}"

        response = requests.get(full_url, headers=headers, verify=False, timeout=5)
        response.raise_for_status()
        
        data = response.json()
        total_sales = data.get('totalSalesQuantity', 0)
        
        # 同样，将总销量平均分配给每个成员
        idol_sales = {idol: total_sales for idol in idol_list}
        
        return idol_sales, True
    except Exception as e:
        print(f"[K-MONSTAR ORG API Error] {str(e)}")
        return {idol: 0 for idol in idol_list}, False

def log_writer():
    """从队列中获取数据并写入对应文件"""
    while True:
        try:
            idol, log_entry = log_queue.get(timeout=1)
            # 将偶像名称转换为小写以匹配文件路径字典的键
            idol_lower = idol.lower()
            if idol_lower in file_paths:
                with open(file_paths[idol_lower], 'a', buffering=1, encoding='utf-8') as f:
                    f.write(log_entry + '\n')
            else:
                print(f"[Log Writer Error] 找不到偶像 {idol} 的文件路径配置")
            log_queue.task_done()
        except queue.Empty:
            time.sleep(0.1)
        except Exception as e:
            print(f"[Log Writer Error] {str(e)}")
            time.sleep(1)

def calculate_changes(idol, current_tw, current_org):
    """计算变化量并返回是否需要记录"""
    prev = previous_data[idol]

    # 首次记录逻辑
    if first_records[idol]:
        tw_change = 0
        org_change = 0
        first_records[idol] = False
        need_record = True
    else:
        # 计算变化量
        tw_change = current_tw - (prev["kmonstar_tw"] or 0) if prev["kmonstar_tw"] is not None else 0
        org_change = current_org - (prev["kmonstar_org"] or 0) if prev["kmonstar_org"] is not None else 0
        # 判断是否需要记录
        need_record = tw_change != 0 or org_change != 0

    # 更新缓存
    previous_data[idol]["kmonstar_tw"] = current_tw
    previous_data[idol]["kmonstar_org"] = current_org

    return tw_change, org_change, need_record

def print_performance_stats():
    """打印性能统计信息"""
    global total_requests, start_monitoring_time

    # 计算平均请求间隔
    avg_interval = sum(request_intervals) / len(request_intervals) if request_intervals else 0

    # 计算每分钟请求数
    elapsed_minutes = (time.time() - start_monitoring_time) / 60
    rpm = total_requests / elapsed_minutes if elapsed_minutes > 0 else 0

    print(f"[性能统计] 平均请求间隔: {avg_interval:.2f}秒 | 每分钟请求数: {rpm:.1f} | 总请求: {total_requests}")

def main():
    # 启动日志写入线程
    writer_thread = threading.Thread(target=log_writer, daemon=True)
    writer_thread.start()

    global last_request_time, total_requests
    last_stats_time = time.time()

    # 主循环
    while True:
        start_time = time.time()

        # 更新请求间隔统计
        current_interval = start_time - last_request_time
        request_intervals.append(current_interval)
        last_request_time = start_time
        total_requests += 1

        # 每100次请求或每5分钟打印一次性能统计
        if total_requests % 100 == 0 or (time.time() - last_stats_time) > 300:
            print_performance_stats()
            last_stats_time = time.time()

        # 获取当前时间
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

        # 获取所有数据（仅请求一次API）
        all_tw_data, tw_valid = fetch_kmonstar_tw()
        all_org_data, org_valid = fetch_kmonstar_org()

        # 处理每个偶像的数据
        for idol in idol_list:
            tw_stock = all_tw_data.get(idol, 0) if tw_valid else previous_data[idol]["kmonstar_tw"] or 0
            org_stock = all_org_data.get(idol, 0) if org_valid else previous_data[idol]["kmonstar_org"] or 0

            # 计算变化量
            tw_change, org_change, need_record = calculate_changes(idol, tw_stock, org_stock)

            # 记录数据
            if need_record:
                log_entry = f"{current_time},{tw_stock},{tw_change},{org_stock},{org_change}"
                print(f"[{idol}] {log_entry}")
                log_queue.put((idol, log_entry))

        # 计算循环耗时，控制请求频率
        elapsed = time.time() - start_time

        # 基础间隔（设置为4-5秒之间）
        min_interval = 4  # 最小间隔
        max_interval = 5  # 最大间隔

        # 添加判断逻辑以动态调整频率
        if not tw_valid and not org_valid:
            # 两个API都失败时，大幅增加间隔
            sleep_time = 5
            print("[Warning] 所有API请求失败，休眠5秒...")
        else:
            # 正常情况下，确保间隔在min_interval和max_interval之间
            # 如果处理时间已经超过了最小间隔，则使用一个很小的延迟
            if elapsed >= min_interval:
                sleep_time = 0.1  # 最小延迟，确保至少有一点点间隔
            else:
                # 计算需要的睡眠时间，保证总时间在min_interval和max_interval之间
                target_interval = random.uniform(min_interval, max_interval)
                sleep_time = max(0.1, target_interval - elapsed)

        # 应用休眠时间
        time.sleep(sleep_time)

if __name__ == "__main__":
    main()