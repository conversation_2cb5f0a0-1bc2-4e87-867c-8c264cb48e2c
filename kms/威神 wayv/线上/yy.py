import requests
import time
from datetime import datetime, timedelta
import urllib3  # Disable SSL warnings

# Disable SSL verification warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# First URL setup
url1 = 'https://kms.kmstation.net/prod/prodInfo?prodId=3628'
headers1 = {
    'locale': 'zh_CN',
    'Origin': 'http://page.kmstation.net',
    'Referer': 'http://page.kmstation.net/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0'
}
previous_total_stock = None

# Combined file path
file_path = 'mayy.txt'

# Second URL setup
url2 = 'https://thor.weidian.com/detail/getItemSkuInfo/1.0'
params2 = {
    'wdtoken': 'a826cb51',
    '_': '1724416335783'
}
headers2 = {
    'accept': 'application/json, /',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8',
    'origin': 'https://shop1382036085.v.weidian.com',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36'
}
item_ids = ["7411045441"]
previous_stocks = {}

# Function to fetch SKU data from the first URL
def fetch_sku_data():
    try:
        response = requests.get(url1, headers=headers1, verify=False)  # Ignore SSL certificate verification
        response.raise_for_status()
        return response.json().get('skuList', [])
    except Exception as e:
        print(f"Exception occurred while fetching {url1}: {str(e)}")
        return None  # Return None to indicate failure

# Function to fetch SKU data from the second URL
def get_stock(item_id):
    try:
        params2['param'] = f'{{"itemId":"{item_id}"}}'
        response = requests.get(url2, headers=headers2, params=params2)
        response.raise_for_status()
        data = response.json()

        total_stock = 0  # Initialize total stock to 0

        # Search for the keyword "YANGYANG" in the title
        for sku_info in data['result']['skuInfos']:
            sku_title = sku_info['skuInfo']['title']  # Get the SKU title

            # Check if the title contains the keyword "YANGYANG"
            if 'YANGYANG' in sku_title.upper():
                total_stock += sku_info['skuInfo']['stock']  # Accumulate stock if the title contains "YANGYANG"

        return total_stock
    except Exception as e:
        print(f"Error retrieving stock data for {item_id}: {e}")
    return 0  # Return 0 if no valid data is found

# Function to log data in a standardized format
def log_data(current_time, total_stock, change_num, stock_weidian, change_weidian, file_path):
    output = f"{current_time},{total_stock},{change_num},{stock_weidian},{change_weidian}"
    print(output)
    with open(file_path, 'a') as file:
        file.write(output + '\n')

# Main function to monitor both inventories
def monitor_stocks():
    global previous_total_stock, previous_stocks

    while True:
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')
        current_total_stock = None
        change_num = 0
        first_changed = False

        # Fetch and process the first URL's stock data
        sku_data = fetch_sku_data()
        if sku_data is None:  # Skip if fetch failed
            time.sleep(2)
            continue

        current_total_stock = sum(sku['stocks'] for sku in sku_data if 'YANGYANG' in sku.get('skuName', '').upper())

        # Check for abnormal stock changes (e.g., sudden drop to 0 and then back)
        if previous_total_stock is not None:
            change_num = current_total_stock - previous_total_stock
            if abs(change_num) > 1000000:  # Skip if change is too large (e.g., 1000031)
                print(f"Skipping abnormal change: {current_time}, change_num={change_num}")
                time.sleep(2)
                continue

        change_num = current_total_stock - previous_total_stock if previous_total_stock is not None else 0
        first_changed = (previous_total_stock is None) or (change_num != 0)
        previous_total_stock = current_total_stock

        # Fetch and process the second URL's stock data
        current_stocks = {}
        stock_changes = {}
        for item_id in item_ids:
            stock = get_stock(item_id)
            if stock is not None:
                current_stocks[item_id] = stock
                if item_id in previous_stocks:
                    change = stock - previous_stocks[item_id]
                    if change != 0:
                        stock_changes[item_id] = change
                previous_stocks[item_id] = stock

        stock_weidian = current_stocks.get(item_ids[0], previous_stocks.get(item_ids[0], 0))
        change_weidian = stock_changes.get(item_ids[0], 0)
        second_changed = bool(stock_changes)

        # Check for future timestamp
        try:
            log_time = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S:')
            if log_time > datetime.now() + timedelta(minutes=1):
                print(f"Future timestamp detected, skipping: {current_time}")
                continue
        except ValueError:
            print(f"Invalid timestamp format, skipping: {current_time}")
            continue

        # Check for abnormal stock changes
        if abs(change_num) > 10000 or abs(change_weidian) > 10000:
            print(f"Abnormal change detected, skipping: {change_num}, {change_weidian}")
            continue

        # Log only valid data
        if current_total_stock is not None and (first_changed or second_changed):
            log_data(current_time, current_total_stock, change_num, stock_weidian, change_weidian, file_path)

        time.sleep(2)

if __name__ == "__main__":
    monitor_stocks()