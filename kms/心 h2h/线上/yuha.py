import requests
import time
import random
from datetime import datetime
import urllib3
from retrying import retry  # 需要安装retrying库：pip install retrying

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 第一个API配置
url1 = 'https://kms.kmstation.net/prod/prodInfo?prodId=3752 '
headers1 = {
    'locale': 'zh_CN',
    'Origin': 'http://page.kmstation.net',
    'Referer': 'http://page.kmstation.net/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0'
}

# 第二个API配置
url2 = 'https://thor.weidian.com/detail/getItemSkuInfo/1.0'
params2_template = {
    'wdtoken': 'a826cb51',
    '_': '1724416335783'
}
headers2 = {
    'accept': 'application/json, /',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8',
    'origin': 'https://shop1382036085.v.weidian.com',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

# 全局配置
file_path = 'yuha.txt'
item_ids = ["7472558780"]
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
]

class DataMonitor:
    def __init__(self):
        self.prev_kms = None
        self.prev_weidian = None
        self.first_record = True  # 首次记录标志

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def fetch_kms(self):
        """获取KMS库存数据（含指数退避重试）"""
        try:
            headers = headers1.copy()
            headers['User-Agent'] = random.choice(user_agents)
            response = requests.get(url1, headers=headers, verify=False, timeout=5)
            response.raise_for_status()

            sku_list = response.json().get('skuList', [])
            valid_stock = sum(sku['stocks'] for sku in sku_list if 'YUHA' in sku.get('skuName', '').upper())

            # 数据校验：库存不能为负
            if valid_stock < 0:
                raise ValueError(f"Invalid KMS stock value: {valid_stock}")

            return valid_stock
        except Exception as e:
            print(f"[KMS API Error] {str(e)}")
            raise

    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def fetch_weidian(self):
        """获取微店库存数据（含指数退避重试）"""
        try:
            headers = headers2.copy()
            headers['User-Agent'] = random.choice(user_agents)
            params = params2_template.copy()
            params['param'] = f'{{"itemId":"{item_ids[0]}"}}'

            response = requests.get(url2, headers=headers, params=params, timeout=5)
            response.raise_for_status()

            sku_infos = response.json()['result']['skuInfos']
            total_stock = sum(
                sku['skuInfo']['stock']
                for sku in sku_infos
                if 'YUHA' in sku['skuInfo'].get('title', '').upper()
            )

            # 数据校验：库存不能为负
            if total_stock < 0:
                raise ValueError(f"Invalid Weidian stock value: {total_stock}")

            return total_stock
        except Exception as e:
            print(f"[Weidian API Error] {str(e)}")
            raise

    def get_changes(self, current, previous):
        """计算有效变化量"""
        if previous is None:  # 首次获取
            return current, 0
        return current, current - previous

    def safe_fetch(self, fetch_func, prev_value):
        """带熔断机制的数据获取"""
        try:
            return fetch_func(), True
        except Exception as e:
            print(f"[Data Fetch Failed] Using cached value. Error: {str(e)}")
            return prev_value, False

    def log_data(self, current_time, total_stock, change_num, stock_weidian, change_weidian):
        """记录数据到文件"""
        log_entry = f"{current_time},{total_stock},{change_num},{stock_weidian},{change_weidian}"
        print(log_entry)
        with open(file_path, 'a', buffering=1) as f:
            f.write(log_entry + '\n')

    def run(self):
        while True:
            # 随机间隔（0.5-2秒）降低检测概率
            time.sleep(random.uniform(0.5, 2))

            # 获取当前时间（统一时间戳）
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

            # 获取KMS数据（失败时使用缓存值）
            kms_stock, kms_valid = self.safe_fetch(self.fetch_kms, self.prev_kms)
            wd_stock, wd_valid = self.safe_fetch(self.fetch_weidian, self.prev_weidian)

            # 计算变化量
            kms_stock, kms_change = self.get_changes(kms_stock, self.prev_kms)
            wd_stock, wd_change = self.get_changes(wd_stock, self.prev_weidian)

            # 更新缓存值（仅当数据有效时）
            if kms_valid:
                self.prev_kms = kms_stock
            if wd_valid:
                self.prev_weidian = wd_stock

            # 首次记录逻辑
            if self.first_record:
                if kms_valid or wd_valid:  # 只要有一个有效数据
                    self.log_data(current_time, kms_stock or 0, kms_change, wd_stock or 0, wd_change)
                    self.first_record = False  # 关闭首次记录标志

            # 记录条件：至少有一个有效数据源有变化
            if (kms_valid and kms_change != 0) or (wd_valid and wd_change != 0):
                self.log_data(current_time, kms_stock, kms_change, wd_stock, wd_change)

            # 动态调整请求频率
            global_sleep = 5  # 调整为30秒
            if not kms_valid and not wd_valid:
                global_sleep = 5  # 双API失败时延长等待
            elif not kms_valid or not wd_valid:
                global_sleep = 5# 单API失败时中等等待
            time.sleep(global_sleep)

def main():
    monitor = DataMonitor()
    monitor.run()

if __name__ == "__main__":
    main()