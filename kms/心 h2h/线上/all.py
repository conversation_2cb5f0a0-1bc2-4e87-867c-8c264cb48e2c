import requests
import time
import random
from datetime import datetime
import urllib3
import threading
import queue
import json
from collections import deque  # 用于计算平均请求间隔

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 全局配置
idol_list = ["ANA", "CARMEN", "IAN", "JIWOO", "JUUN",'STELLA','YEON','YUHA']  # 所有偶像列表
file_paths = {
    "ANA": "ana.txt",
    "CARMEN": "carmen.txt",
    "IAN": "ian.txt",
    "JUUN": "juun.txt",
    "STELLA": "stella.txt",
    'YEON':'yeon.txt',
    'YUHA':'yuha.txt'
}

# 每个偶像对对应的kms ID
kms_prod_ids = {
    "ANA": "ana.txt",
    "CARMEN": "carmen.txt",
    "IAN": "ian.txt",
    "JUUN": "juun.txt",
    "STELLA": "stella.txt",
    'YEON':'yeon.txt',
    'YUHA':'yuha.txt'
}
# 每个偶像对对应的微店 ID
weidian_item_ids = {
    "ANA": "ana.txt",
    "CARMEN": "carmen.txt",
    "IAN": "ian.txt",
    "JUUN": "juun.txt",
    "STELLA": "stella.txt",
    'YEON':'yeon.txt',
    'YUHA':'yuha.txt'
}

# 请求配置
url_kms_base = 'https://kms.kmstation.net/prod/prodInfo?prodId='
headers_kms = {
    'locale': 'zh_CN',
    'Origin': 'http://page.kmstation.net',
    'Referer': 'http://page.kmstation.net/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36'
}

url_weidian = 'https://thor.weidian.com/detail/getItemSkuInfo/1.0'
params_weidian_base = {
    'wdtoken': 'a826cb51',
    '_': str(int(time.time() * 1000))
}
headers_weidian = {
    'accept': 'application/json, /',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'cookie': 'wdtoken=a826cb51; __spider__visitorid=02e28d6876657bf8',
    'origin': 'https://shop1382036085.v.weidian.com',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36'
}

# 随机UA池
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/127.0.0.0 Safari/537.36'
]

# 数据缓存
previous_data = {idol: {"kms": None, "weidian": None} for idol in idol_list}
first_records = {idol: True for idol in idol_list}

# 线程安全的队列，用于存放待写入的数据
log_queue = queue.Queue()

# 添加性能监控变量
last_request_time = time.time()
request_intervals = deque(maxlen=50)  # 保存最近50次请求的间隔
total_requests = 0
start_monitoring_time = time.time()

def fetch_kms_data(idol):
    """获取指定偶像的KMS数据"""
    try:
        headers = headers_kms.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 添加随机参数避免缓存
        random_param = f"nocache={random.randint(1000000, 9999999)}"
        full_url = f"{url_kms_base}{kms_prod_ids[idol]}&{random_param}"

        response = requests.get(full_url, headers=headers, verify=False, timeout=5)
        response.raise_for_status()

        data = response.json()
        sku_list = data.get('skuList', [])

        # 计算总库存
        total_stock = sum(sku['stocks'] for sku in sku_list)

        return total_stock, True
    except Exception as e:
        print(f"[KMS API Error] {idol}: {str(e)}")
        return 0, False

def fetch_weidian_data(idol):
    """获取指定偶像的微店数据"""
    # 检查是否为空ID
    if not weidian_item_ids.get(idol) or not weidian_item_ids[idol].strip():
        # 如果ID为空，直接返回0库存，但不标记为错误
        return 0, True

    try:
        headers = headers_weidian.copy()
        headers['User-Agent'] = random.choice(user_agents)

        # 更新时间戳参数
        params = params_weidian_base.copy()
        params['_'] = str(int(time.time() * 1000))
        params['param'] = f'{{"itemId":"{weidian_item_ids[idol]}"}}'

        response = requests.get(url_weidian, headers=headers, params=params, timeout=5)
        response.raise_for_status()

        sku_infos = response.json()['result']['skuInfos']

        # 计算总库存
        total_stock = sum(sku['skuInfo']['stock'] for sku in sku_infos)

        return total_stock, True
    except Exception as e:
        print(f"[Weidian API Error] {idol}: {str(e)}")
        return 0, False

def log_writer():
    """从队列中获取数据并写入对应文件"""
    while True:
        try:
            idol, log_entry = log_queue.get(timeout=1)
            with open(file_paths[idol], 'a', buffering=1, encoding='utf-8') as f:
                f.write(log_entry + '\n')
            log_queue.task_done()
        except queue.Empty:
            time.sleep(0.1)
        except Exception as e:
            print(f"[Log Writer Error] {str(e)}")
            time.sleep(1)

def calculate_changes(idol, current_kms, current_weidian):
    """计算变化量并返回是否需要记录"""
    prev = previous_data[idol]

    # 首次记录逻辑
    if first_records[idol]:
        kms_change = 0
        wd_change = 0
        first_records[idol] = False
        need_record = True
    else:
        # 计算变化量
        kms_change = current_kms - (prev["kms"] or 0) if prev["kms"] is not None else 0
        wd_change = current_weidian - (prev["weidian"] or 0) if prev["weidian"] is not None else 0
        # 判断是否需要记录
        need_record = kms_change != 0 or wd_change != 0

    # 更新缓存
    previous_data[idol]["kms"] = current_kms
    previous_data[idol]["weidian"] = current_weidian

    return kms_change, wd_change, need_record

def print_performance_stats():
    """打印性能统计信息"""
    global total_requests, start_monitoring_time

    # 计算平均请求间隔
    avg_interval = sum(request_intervals) / len(request_intervals) if request_intervals else 0

    # 计算每分钟请求数
    elapsed_minutes = (time.time() - start_monitoring_time) / 60
    rpm = total_requests / elapsed_minutes if elapsed_minutes > 0 else 0

    print(f"[性能统计] 平均请求间隔: {avg_interval:.2f}秒 | 每分钟请求数: {rpm:.1f} | 总请求: {total_requests}")

def create_empty_files_if_not_exist():
    """如果文件不存在，则创建空文件"""
    for idol, file_path in file_paths.items():
        try:
            with open(file_path, 'a', encoding='utf-8'):
                pass  # 只打开文件进行写入，如果不存在则创建
            print(f"[初始化] 检查文件 {file_path} 完成")
        except Exception as e:
            print(f"[初始化错误] 无法创建文件 {file_path}: {str(e)}")

def main():
    print("[WayV监控] 开始运行监控程序...")
    print(f"[初始化] 监控偶像: {', '.join(idol_list)}")

    # 检查微店ID配置
    empty_ids = [idol for idol in idol_list if not weidian_item_ids.get(idol) or not weidian_item_ids[idol].strip()]
    if empty_ids:
        print(f"[配置] 以下偶像的微店商品ID未设置，将跳过其微店API请求: {', '.join(empty_ids)}")

    # 确保所有文件存在
    create_empty_files_if_not_exist()

    # 启动日志写入线程
    writer_thread = threading.Thread(target=log_writer, daemon=True)
    writer_thread.start()
    print("[初始化] 日志写入线程已启动")

    global last_request_time, total_requests
    last_stats_time = time.time()

    # 主循环
    try:
        print("[运行] 开始主循环监控...")
        while True:
            start_time = time.time()

            # 更新请求间隔统计
            current_interval = start_time - last_request_time
            request_intervals.append(current_interval)
            last_request_time = start_time
            total_requests += 1

            # 每100次请求或每5分钟打印一次性能统计
            if total_requests % 100 == 0 or (time.time() - last_stats_time) > 300:
                print_performance_stats()
                last_stats_time = time.time()

            # 获取当前时间
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S:')

            # 为每个偶像分别获取数据
            for idol in idol_list:
                # 添加随机延迟，避免请求过于集中
                time.sleep(random.uniform(0.2, 0.4))

                # 获取KMS数据
                kms_stock, kms_valid = fetch_kms_data(idol)

                # 添加随机延迟，避免请求过于集中
                time.sleep(random.uniform(0.2, 0.4))

                # 获取微店数据
                wd_stock, wd_valid = fetch_weidian_data(idol)

                # 如果两个API都失败，使用上次的数据
                if not kms_valid and not wd_valid:
                    continue

                # 计算变化量
                kms_change, wd_change, need_record = calculate_changes(
                    idol,
                    kms_stock if kms_valid else previous_data[idol]["kms"] or 0,
                    wd_stock if wd_valid else previous_data[idol]["weidian"] or 0
                )

                # 记录数据
                if need_record:
                    log_entry = f"{current_time},{kms_stock},{kms_change},{wd_stock},{wd_change}"
                    print(f"[{idol}] {log_entry}")
                    log_queue.put((idol, log_entry))

            # 计算循环耗时，控制请求频率
            elapsed = time.time() - start_time

            # 基础间隔（设置为2-3秒之间，因为现在每个循环有更多的请求）
            min_interval = 8  # 最小间隔
            max_interval = 9  # 最大间隔

            # 计算需要的睡眠时间
            if elapsed >= min_interval:
                sleep_time = 0.5  # 最小延迟
            else:
                # 计算需要的睡眠时间，保证总时间在min_interval和max_interval之间
                target_interval = random.uniform(min_interval, max_interval)
                sleep_time = max(0.5, target_interval - elapsed)

            # 应用休眠时间
            time.sleep(sleep_time)

    except KeyboardInterrupt:
        print("\n[停止] 收到终止信号，正在关闭程序...")
    except Exception as e:
        print(f"[严重错误] 程序异常: {str(e)}")
    finally:
        # 等待所有日志写入完成
        print("[关闭] 等待剩余日志写入...")
        log_queue.join()
        print("[关闭] 程序已终止")

if __name__ == "__main__":
    main()