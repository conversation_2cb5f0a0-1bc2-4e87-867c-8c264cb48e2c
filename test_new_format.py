#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('flash/常用')

from new import InventoryProcessor

def test_new_format():
    """测试新的txt格式处理"""
    
    # 创建处理器实例
    processor = InventoryProcessor(threshold=190, highlight_num=40)
    
    # 模拟新格式的数据
    test_data = [
        "2025-08-12 04:01:06:,500743,0,411768,0,500481,0",
        "2025-08-12 04:02:06:,500743,-10,411768,-5,500481,-8",
        "2025-08-12 04:03:06:,500743,5,411768,3,500481,2",
        "2025-08-12 04:04:06:,500743,-200,411768,-150,500481,-180",
    ]
    
    print("测试数据:")
    for line in test_data:
        print(f"  {line}")
    
    print("\n开始处理数据...")
    
    try:
        processed_data, combined, total_sales, app_sales = processor.process_data(test_data)
        
        print(f"\n处理结果:")
        print(f"总销量: {total_sales}")
        print(f"APP销量: {app_sales}")
        print(f"高光数据数量: {len(combined)}")
        print(f"处理的数据行数: {len(processed_data)}")
        
        print(f"\n详细数据:")
        for line, filtered, real_sold, real_stock, real_new_app_change in processed_data:
            parts = line.split(',')
            print(f"时间: {parts[0]}")
            if real_sold != 0:
                print(f"  旧版app变化: {real_sold} ({'注水' if filtered and abs(real_sold) > processor.threshold else '正常' if real_sold < 0 else '退回'})")
            if real_stock != 0:
                print(f"  微店变化: {real_stock} ({'注水' if filtered and abs(real_stock) > processor.threshold else '正常' if real_stock < 0 else '退回'})")
            if real_new_app_change != 0:
                print(f"  新版app变化: {real_new_app_change} ({'注水' if filtered and abs(real_new_app_change) > processor.threshold else '正常' if real_new_app_change < 0 else '退回'})")
            print()
        
        print("测试成功!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_format()
