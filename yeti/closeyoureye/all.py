import requests
import time
import random
from datetime import datetime
import urllib3
import re
import os
import sys
from retrying import retry  # 需要安装retrying库：pip install retrying

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API配置
url = 'https://yetimall.fun/public/home/<USER>/goodsDetail'
params = {
    'id': '25663'
}
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Referer': 'https://yetimall.fun/',
    'Origin': 'https://yetimall.fun',
    'Connection': 'keep-alive'
}

# 全局配置
file_path = 'closeyoureye.txt'
user_agents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:128.0) Gecko/20100101 Firefox/128.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Wi64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
]

# 可调整的配置参数
config = {
    'min_interval': 0.1,  # 最小刷新间隔（秒）
    'max_interval': 0.4,  # 最大刷新间隔（秒）
    'error_wait': 5,      # 错误后等待时间（秒）
    'config_check_interval': 30,  # 检查配置文件的间隔（秒）
}

# 配置文件路径
config_file = '1.log'

def load_config():
    """从配置文件加载设置"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                for line in f:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if key in config and value.replace('.', '', 1).isdigit():
                            config[key] = float(value)
            print(f"[配置已加载] 刷新间隔: {config['min_interval']}-{config['max_interval']}秒")
        except Exception as e:
            print(f"[配置加载失败] {str(e)}")

def create_default_config():
    """创建默认配置文件"""


class InventoryMonitor:
    def __init__(self):
        self.prev_inventory = None
        self.first_record = True  # 首次记录标志
        self.last_config_check = time.time()
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_status_print = time.time()

        # 初始化文件头


    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def fetch_inventory(self):
        """获取库存数据（含指数退避重试）"""
        try:
            self.request_count += 1

            # 随机选择User-Agent
            current_headers = headers.copy()
            current_headers['User-Agent'] = random.choice(user_agents)

            # 添加随机查询参数以避免缓存
            current_params = params.copy()
            current_params['_'] = str(int(time.time() * 1000))
            current_params['r'] = str(random.randint(10000, 99999))

            # 随机化请求参数顺序
            param_keys = list(current_params.keys())
            random.shuffle(param_keys)
            shuffled_params = {k: current_params[k] for k in param_keys}

            # 添加随机Accept-Encoding和Accept-Language变化
            if random.random() > 0.5:
                current_headers['Accept-Encoding'] = 'gzip, deflate, br'

            languages = ['zh-CN,zh;q=0.9,en;q=0.8', 'en-US,en;q=0.9', 'zh-TW,zh;q=0.9,en;q=0.8']
            current_headers['Accept-Language'] = random.choice(languages)

            response = requests.get(
                url,
                headers=current_headers,
                params=shuffled_params,
                timeout=5,
                verify=False
            )
            response.raise_for_status()

            # 从HTML中提取库存数据
            html = response.text
            inventory_match = re.search('data-inventory="(\d+)"', html)

            if not inventory_match:
                raise ValueError("无法从HTML中提取库存数据")

            inventory = int(inventory_match.group(1))

            # 数据校验：库存不能为负
            if inventory < 0:
                raise ValueError(f"Invalid inventory value: {inventory}")

            self.success_count += 1
            return inventory
        except Exception as e:
            self.error_count += 1
            print(f"[API Error] {str(e)}")
            raise

    def get_changes(self, current, previous):
        """计算有效变化量"""
        if previous is None:  # 首次获取
            return current, 0
        return current, current - previous

    def safe_fetch(self):
        """带熔断机制的数据获取"""
        try:
            inventory = self.fetch_inventory()
            return inventory, True
        except Exception as e:
            print(f"[Data Fetch Failed] Using cached value. Error: {str(e)}")
            return self.prev_inventory, False

    def log_data(self, current_time, inventory, inventory_change):
        """记录数据到文件"""
        log_entry = f"{current_time},{inventory},{inventory_change},0,0"
        print(log_entry)
        with open(file_path, 'a', buffering=1) as f:
            f.write(log_entry + '\n')

    def print_status(self):
        """打印监控状态"""
        now = time.time()
        if now - self.last_status_print >= 60:  # 每分钟打印一次状态
            runtime = now - self.start_time
            hours = int(runtime // 3600)
            minutes = int((runtime % 3600) // 60)
            seconds = int(runtime % 60)

            print(f"[状态] 运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}, "
                  f"请求: {self.request_count}, 成功: {self.success_count}, "
                  f"错误: {self.error_count}, 当前库存: {self.prev_inventory or '未知'}")
            self.last_status_print = now

    def run(self):
        self.start_time = time.time()
        print(f"[开始监控] 刷新间隔: {config['min_interval']}-{config['max_interval']}秒")

        while True:
            try:
                # 检查配置文件是否有更新
                now = time.time()
                if now - self.last_config_check >= config['config_check_interval']:
                    load_config()
                    self.last_config_check = now

                # 随机间隔降低检测概率
                sleep_time = random.uniform(config['min_interval'], config['max_interval'])
                time.sleep(sleep_time)

                # 获取当前时间（统一时间戳）
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 获取库存数据（失败时使用缓存值）
                inventory, is_valid = self.safe_fetch()

                if is_valid:
                    # 计算变化量
                    inventory, inventory_change = self.get_changes(inventory, self.prev_inventory)

                    # 更新缓存值
                    self.prev_inventory = inventory

                    # 首次记录逻辑
                    if self.first_record:
                        self.log_data(current_time, inventory, 0)
                        self.first_record = False
                    # 记录条件：库存有变化
                    elif inventory_change != 0:
                        self.log_data(current_time, inventory, inventory_change)

                # 打印状态信息
                self.print_status()

                # 动态调整请求频率
                if not is_valid:
                    # API失败时延长等待
                    time.sleep(random.uniform(config['error_wait'], config['error_wait'] * 2))

            except KeyboardInterrupt:
                print("\n[监控已停止] 用户中断")
                sys.exit(0)
            except Exception as e:
                print(f"[Runtime Error] {str(e)}")
                time.sleep(config['error_wait'])  # 出现运行时错误时等待较长时间

def main():
    # 创建和加载配置文件
    create_default_config()
    load_config()

    # 启动监控
    monitor = InventoryMonitor()
    monitor.run()

if __name__ == "__main__":
    main()