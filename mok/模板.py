import requests
import re
import json
import time
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 配置参数
TARGET_URL = "https://en.mokketshop.com/product/0413-video-call-fan-signing-event-minnie-1st-mini-album-her-random-ver/3301/category/331/display/1/"
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Referer': TARGET_URL
}
LOG_FILE = 'minnie.txt'
CHECK_INTERVAL = 10  # 检查间隔(秒)
previous_stock = None

class StockMonitor:
    def __init__(self):
        self.session = requests.Session()

    def extract_stock_data(self, html):
        """从HTML中提取库存数据"""
        try:
            # 匹配JavaScript变量中的库存数据
            pattern = r"var option_stock_data\s*=\s*'(.*?)';"
            match = re.search(pattern, html, re.DOTALL)
            if not match:
                print("未找到库存数据")
                return None

            # 处理转义字符和编码问题
            json_str = match.group(1)
            # 转换JavaScript转义字符为有效JSON格式
            json_str = json_str.encode('latin1').decode('unicode_escape')

            # 解析JSON数据
            stock_data = json.loads(json_str)

            # 计算总库存（包含所有选项）
            total_stock = sum(
                int(item.get('stock_number', 0))
                for item in stock_data.values()
            )
            return total_stock

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {str(e)}")
            print("处理后的数据:", json_str)
            return None
        except Exception as e:
            print(f"数据解析失败: {str(e)}")
            return None

    def fetch_stock(self):
        """获取当前库存"""
        try:
            response = self.session.get(
                TARGET_URL,
                headers=HEADERS,
                verify=False,
                timeout=15
            )
            response.raise_for_status()
            return self.extract_stock_data(response.text)

        except requests.RequestException as e:
            print(f"请求失败: {str(e)}")
            return None

    def log_stock(self, stock):
        """记录库存信息"""
        global previous_stock

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        change = stock - previous_stock if previous_stock is not None else 0

        log_entry = f"{timestamp},{stock},{change:}"
        print(log_entry)

        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(f"{timestamp},{stock},{change},0,0\n")

        previous_stock = stock

    def monitor(self):
        """监控主循环"""
        print("=== Minnie签售show time ===")
        print("="*40)

        try:
            while True:
                current_stock = self.fetch_stock()

                if current_stock is not None:
                    if previous_stock is None or current_stock != previous_stock:
                        self.log_stock(current_stock)

                time.sleep(CHECK_INTERVAL)

        except KeyboardInterrupt:
            print("\n监控已手动停止")
        except Exception as e:
            print(f"监控异常: {str(e)}")

if __name__ == "__main__":
    monitor = StockMonitor()
    monitor.monitor()