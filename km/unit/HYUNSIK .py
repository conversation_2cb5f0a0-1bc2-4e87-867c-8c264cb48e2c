import requests
import json
import time
from datetime import datetime
import random

def get_event_sales_quantity(event_id):
    """获取K-MONSTAR特定活动产品的销售数量"""
    base_api_url = "https://www.kmonstar.org/api/v1/event/detail/"
    target_url = f"{base_api_url}{event_id}"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Referer": f"https://www.kmonstar.org/ko/eventproductdetail/{event_id}",
        "Origin": "https://www.kmonstar.org",
        "Connection": "keep-alive",
    }

    try:
        response = requests.get(target_url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and "data" in data:
                event_data = data["data"]
                # 查找HYUNSIK选项的销量
                if "optionList" in event_data and len(event_data["optionList"]) > 0:
                    for option in event_data["optionList"]:
                        # 检查选项是否包含HYUNSIK
                        if ("optionNameValue1" in option and
                                "HYUNSIK" in option["optionNameValue1"]):
                            if "salesQuantity" in option:
                                try:
                                    return int(option["salesQuantity"])
                                except (ValueError, TypeError):
                                    print(f"HYUNSIK销售数量格式异常: {option['salesQuantity']}")

                # 如果没找到HYUNSIK特定数据，尝试返回总销量
                if "totalSalesQuantity" in event_data:
                    try:
                        return int(event_data["totalSalesQuantity"])
                    except (ValueError, TypeError):
                        print(f"总销售数量格式异常: {event_data['totalSalesQuantity']}")

                print("API返回的数据结构中缺少销售数量相关字段")
            else:
                print(f"API返回非成功状态: {data.get('message')}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except requests.RequestException as e:
        print(f"请求异常: {e}")
    except json.JSONDecodeError:
        print("JSON解析失败")
    except Exception as e:
        print(f"发生未知错误: {e}")
    return None

def get_product_sold_data(api_url):
    """从指定API获取产品销量数据"""
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "keep-alive",
        }
        response = requests.get(api_url, headers=headers, timeout=10)
        if response.status_code == 200:
            product_data = response.json()
            # 查找HYUNSIK选项的库存
            if 'variants' in product_data and isinstance(product_data['variants'], list):
                for variant in product_data['variants']:
                    if variant.get('option1') == 'HYUNSIK':
                        try:
                            return int(variant.get('inventory_quantity', 0))
                        except (ValueError, TypeError):
                            print(f"HYUNSIK库存数量格式异常: {variant.get('inventory_quantity')}")
                            return None
                print("未找到HYUNSIK相关选项")
                return None
            else:
                print("错误: API响应中未找到'variants'字段或其不是列表")
                return None
        else:
            print(f"错误: 请求失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"错误: 发生网络请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败: {e}")
        return None

def monitor_sales(event_id, product_api_url, interval=5):
    """定期监控活动产品的销售数量变化，并将变化记录到txt文件和控制台"""
    print(f"开始监控活动 {event_id} 和产品销量数据...")
    previous_event_sales = None
    previous_product_sales = None

    # 初始获取数据并记录
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    current_event_sales = get_event_sales_quantity(event_id)  # 现在这会返回HYUNSIK的销量
    current_product_sales = get_product_sold_data(product_api_url)  # 现在这会返回HYUNSIK的库存

    # 输出初始信息到控制台和文件
    should_log = (current_event_sales is not None) or (current_product_sales is not None)
    if should_log:
        event_value = current_event_sales if current_event_sales is not None else "获取失败"
        product_value = current_product_sales if current_product_sales is not None else "获取失败"

        log_line = f"{current_time}:, {event_value},0,{-product_value},0"
        print(log_line)

        with open("hyunsik.txt", "a", encoding="utf-8") as f:
            f.write(log_line + "\n")

    # 更新上次的销售数量
    previous_event_sales = current_event_sales
    previous_product_sales = current_product_sales

    while True:
        # 等待随机间隔时间
        time.sleep(interval + random.uniform(1, 2))

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        current_event_sales = get_event_sales_quantity(event_id)
        current_product_sales = get_product_sold_data(product_api_url)

        # 检查是否有变化
        event_changed = current_event_sales != previous_event_sales
        product_changed = current_product_sales != previous_product_sales

        # 只要有一个数据成功获取且有变化，或者从失败转为成功，就记录
        should_log = (
                (event_changed and current_event_sales is not None) or
                (product_changed and current_product_sales is not None)
        )

        if should_log:
            # 计算变化量（如果有前值）
            event_sales_change = (
                current_event_sales - previous_event_sales
                if (current_event_sales is not None and previous_event_sales is not None)
                else 0
            )

            product_sales_change = (
                current_product_sales - previous_product_sales
                if (current_product_sales is not None and previous_product_sales is not None)
                else 0
            )

            # 构建日志行
            event_value = current_event_sales if current_event_sales is not None else "获取失败"
            product_value = current_product_sales if current_product_sales is not None else "获取失败"

            log_line = f"{current_time}:, {event_value},{event_sales_change},{-product_value},{-product_sales_change}"
            print(log_line)

            with open("hyunsik.txt", "a", encoding="utf-8") as f:
                f.write(log_line + "\n")

            # 更新上次的销售数量
            if current_event_sales is not None:
                previous_event_sales = current_event_sales
            if current_product_sales is not None:
                previous_product_sales = current_product_sales

if __name__ == "__main__":
    EVENT_ID = "ef87d6f1-796b-46b9-b999-f99fae0bb410"
    PRODUCT_API_URL = "https://www.kmonstar.com.tw/products/%E6%87%89%E5%8B%9F-250817-x-unit-1st-mini-album-tou-11-%E6%8B%8D%E7%AB%8B%E5%BE%97%E5%90%88%E7%85%A7%E6%B4%BB%E5%8B%95-in-taipei.json"

    monitor_sales(EVENT_ID, PRODUCT_API_URL, interval=5)
    